{"name": "scamshield", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "expo start", "start:dev-client": "expo start --dev-client", "start:android": "expo start --android", "start:ios": "expo start --ios", "start:web": "expo start --web", "test": "jest"}, "dependencies": {"@react-native-firebase/app": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "expo": "^53.0.12", "react": "18.2.0", "react-native": "0.73.6", "react-native-permissions": "^5.4.1", "react-native-sqlite-storage": "^6.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@expo/cli": "^0.24.15", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}