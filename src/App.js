import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import Dashboard from './src/screens/Dashboard';
import Settings from './src/screens/Settings';
import { initLocalDB, syncWithCloud } from './src/services/Database';
import { initCallBlocking } from './src/services/CallService';

const Stack = createStackNavigator();

const App = () => {
  useEffect(() => {
    // Initialize database and services
    const initializeApp = async () => {
      await initLocalDB();
      await initCallBlocking();
      await syncWithCloud();
      // Schedule daily sync
      setInterval(syncWithCloud, 24 * 60 * 60 * 1000);
    };
    
    initializeApp();
  }, []);

  return (
    <NavigationContainer>
      <Stack.Navigator>
        <Stack.Screen name="Dashboard" component={Dashboard} />
        <Stack.Screen name="Settings" component={Settings} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default App;