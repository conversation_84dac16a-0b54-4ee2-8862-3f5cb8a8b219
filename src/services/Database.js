import SQLite from 'react-native-sqlite-storage';
import firestore from '@react-native-firebase/firestore';

const db = SQLite.openDatabase({ name: 'scamshield.db', location: 'default' });

// Initialize local DB
export const initLocalDB = () => {
  db.transaction(tx => {
    tx.executeSql(
      `CREATE TABLE IF NOT EXISTS scam_numbers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        number TEXT UNIQUE,
        report_count INTEGER DEFAULT 1,
        last_reported TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        verified BOOLEAN DEFAULT 0,
        scam_type TEXT
      );`
    );
  });
};

// Add scam number to local DB
export const addScamNumber = (number, scamType = 'unknown') => {
  return new Promise((resolve, reject) => {
    db.transaction(tx => {
      tx.executeSql(
        `INSERT OR REPLACE INTO scam_numbers 
        (number, report_count, last_reported, scam_type) 
        VALUES (?, COALESCE((SELECT report_count FROM scam_numbers WHERE number = ?), 0) + 1, 
        datetime('now'), ?)`,
        [number, number, scamType],
        (_, result) => resolve(result),
        (_, error) => reject(error)
      );
    });
  });
};

// Sync with Firestore
export const syncWithCloud = async () => {
  const localNumbers = await getLocalScamNumbers();
  const scamRef = firestore().collection('scam_numbers');
  
  // Upload new reports
  localNumbers.forEach(async num => {
    if (!num.synced) {
      await scamRef.doc(num.number).set({
        report_count: firestore.FieldValue.increment(1),
        last_reported: new Date(),
        scam_type: num.scam_type
      }, { merge: true });
    }
  });

  // Download verified numbers
  const snapshot = await scamRef.where('verified', '==', true).get();
  snapshot.forEach(doc => {
    addScamNumber(doc.id, doc.data().scam_type);
  });
};

// Get local scam numbers
export const getLocalScamNumbers = () => {
  return new Promise((resolve) => {
    db.transaction(tx => {
      tx.executeSql(
        'SELECT * FROM scam_numbers',
        [],
        (_, { rows }) => resolve(rows._array)
      );
    });
  });
};