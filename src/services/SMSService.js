import { NativeModules } from 'react-native';
import { getLocalScamNumbers } from './Database';

const { SMSModule } = NativeModules;

// Handle incoming SMS
export const handleIncomingSMS = async (sms) => {
  if (await isScamNumber(sms.originatingAddress)) {
    SMSModule.moveToSpamFolder(sms);
    return true; // SMS blocked
  }
  return false; // SMS allowed
};

// Check for scam patterns in message content
export const detectScamPatterns = (message) => {
  const patterns = [
    /urgent|verify|account|suspended|limited|offer|prize|won|claim/i,
    /http:\/\/\S+|https:\/\/\S+/,
    /click here|login now|verify now/im
  ];
  
  return patterns.some(pattern => pattern.test(message));
};