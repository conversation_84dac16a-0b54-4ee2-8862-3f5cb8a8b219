import { NativeModules, Platform } from 'react-native';
import { getLocalScamNumbers } from './Database';

const { CallModule } = NativeModules;

// Initialize call blocking
export const initCallBlocking = async () => {
  const scamNumbers = (await getLocalScamNumbers()).map(num => num.number);
  
  if (Platform.OS === 'android') {
    CallModule.initCallBlocking(scamNumbers);
  }
  // iOS would use CallKit here
};

// Handle incoming call
export const handleIncomingCall = (callInfo) => {
  if (isScamNumber(callInfo.phoneNumber)) {
    if (Platform.OS === 'android') {
      CallModule.blockCurrentCall();
    }
    return true; // Call blocked
  }
  return false; // Call allowed
};

// Check if number is scam
const isScamNumber = async (number) => {
  const normalized = normalizeNumber(number);
  const scamNumbers = await getLocalScamNumbers();
  return scamNumbers.some(n => normalizeNumber(n.number) === normalized);
};

// Helper to normalize numbers
const normalizeNumber = (number) => {
  return number.replace(/[^0-9]/g, '').slice(-10); // Last 10 digits
};