import React, { useState, useEffect } from 'react';
import { View, Text, FlatList, Button } from 'react-native';
import { getLocalScamNumbers } from '../services/Database';
import ReportDialog from '../components/ReportDialog';

const Dashboard = () => {
  const [scamNumbers, setScamNumbers] = useState([]);
  const [showReportDialog, setShowReportDialog] = useState(false);

  useEffect(() => {
    loadScamNumbers();
  }, []);

  const loadScamNumbers = async () => {
    const numbers = await getLocalScamNumbers();
    setScamNumbers(numbers);
  };

  return (
    <View style={{ flex: 1, padding: 16 }}>
      <Text style={{ fontSize: 24, marginBottom: 20 }}>Scam Shield</Text>
      
      <Button 
        title="Report Scam Number" 
        onPress={() => setShowReportDialog(true)} 
      />
      
      <Text style={{ marginTop: 20, fontSize: 18 }}>Blocked Numbers:</Text>
      <FlatList
        data={scamNumbers}
        keyExtractor={item => item.number}
        renderItem={({ item }) => (
          <Text style={{ padding: 10 }}>
            {item.number} ({item.report_count} reports)
          </Text>
        )}
      />
      
      <ReportDialog 
        visible={showReportDialog}
        onClose={() => setShowReportDialog(false)}
        onReport={async (number, type) => {
          await addScamNumber(number, type);
          loadScamNumbers();
        }}
      />
    </View>
  );
};

export default Dashboard;