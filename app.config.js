export default {
  expo: {
    name: "ScamShield",
    slug: "scamshield",
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/icon.png",
    userInterfaceStyle: "light",
    splash: {
      image: "./assets/splash.png",
      resizeMode: "contain",
      backgroundColor: "#ffffff"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.scamshield.app"
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/adaptive-icon.png",
        backgroundColor: "#ffffff"
      },
      package: "com.scamshield.app",
      permissions: [
        "READ_PHONE_STATE",
        "CALL_PHONE",
        "READ_CALL_LOG",
        "WRITE_CALL_LOG",
        "ANSWER_PHONE_CALLS",
        "READ_SMS",
        "RECEIVE_SMS",
        "SEND_SMS"
      ]
    },
    web: {
      favicon: "./assets/favicon.png"
    },
    plugins: [
      [
        "@react-native-firebase/app",
        {
          "android": {
            "googleServicesFile": "./google-services.json"
          },
          "ios": {
            "googleServicesFile": "./GoogleService-Info.plist"
          }
        }
      ]
    ]
  }
};
