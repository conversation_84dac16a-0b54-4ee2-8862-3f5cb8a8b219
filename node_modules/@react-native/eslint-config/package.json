{"name": "@react-native/eslint-config", "version": "0.73.2", "description": "ESLint config for React Native", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/facebook/react-native.git", "directory": "packages/eslint-config-react-native"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/eslint-config-react-native#readme", "keywords": ["eslint", "config", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "main": "index.js", "dependencies": {"@babel/core": "^7.20.0", "@babel/eslint-parser": "^7.20.0", "@react-native/eslint-plugin": "0.73.1", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-ft-flow": "^2.0.1", "eslint-plugin-jest": "^26.5.3", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0"}, "peerDependencies": {"eslint": ">=8", "prettier": ">=2"}, "devDependencies": {"eslint": "^8.23.1", "prettier": "2.8.8"}}