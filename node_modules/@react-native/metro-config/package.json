{"name": "@react-native/metro-config", "version": "0.73.5", "description": "Metro configuration for React Native.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/facebook/react-native.git", "directory": "packages/metro-config"}, "homepage": "https://github.com/facebook/react-native/tree/HEAD/packages/metro-config#readme", "keywords": ["metro", "config", "react-native"], "bugs": "https://github.com/facebook/react-native/issues", "engines": {"node": ">=18"}, "exports": "./index.js", "dependencies": {"@react-native/metro-babel-transformer": "0.73.15", "@react-native/js-polyfills": "0.73.1", "metro-config": "^0.80.3", "metro-runtime": "^0.80.3"}}