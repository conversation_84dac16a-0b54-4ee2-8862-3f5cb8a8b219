{"version": 3, "file": "Options.js", "sourceRoot": "", "sources": ["../src/Options.ts"], "names": [], "mappings": ";;;;;;AA4DA,sDAqCC;AAjGD,2DAA6B;AAE7B,4CAAoB;AACpB,gDAAwB;AAExB,qCAA2C;AAC3C,iDAAoD;AAEpD,uDAAgE;AAChE,uDAAoD;AACpD,uCAA6F;AAEhF,QAAA,2BAA2B,GAAG,oBAAoB,CAAC;AAEnD,QAAA,oBAAoB,GAAG;IAClC,mCAA2B;IAC3B,UAAU;IACV,uBAAuB;IACvB,sBAAsB;IACtB,yBAAyB;IACzB,2BAA2B;IAC3B,0BAA0B;IAC1B,6BAA6B;IAC7B,kCAAkC;IAClC,iCAAiC;IACjC,oCAAoC;IACpC,4CAA4C;IAC5C,2CAA2C;IAC3C,8CAA8C;IAE9C,8DAA8D;IAC9D,wBAAwB;IAExB,yBAAyB;IACzB,+BAA+B;IAC/B,8BAA8B;IAC9B,iCAAiC;IAEjC,MAAM;IACN,kBAAkB;IAClB,mBAAmB;IACnB,yBAAyB;IACzB,+BAA+B;IAC/B,sCAAsC;IAEtC,mDAAmD;IACnD,cAAc;IAEd,2FAA2F;IAC3F,eAAe;IACf,eAAe;IACf,iBAAiB;IACjB,UAAU;IAEV,6BAA6B;IAC7B,oCAAoC;CACrC,CAAC;AAEW,QAAA,oBAAoB,GAAG,yBAAW,CAAC,8CAA8C,CAAC;AAExF,KAAK,UAAU,qBAAqB,CACzC,WAAmB,EACnB,OAAiB;IAEjB,MAAM,MAAM,GAAG,MAAM,IAAA,wBAAe,EAAC,WAAW,EAAE,OAAO,EAAE,MAAM,IAAI,KAAK,CAAC,CAAC;IAC5E,MAAM,sBAAsB,GAAG,MAAM,uBAAuB,CAC1D,WAAW,EACX,MAAM,EAAE,WAAW,EACnB,OAAO,CACR,CAAC;IACF,MAAM,kBAAkB,GAAG,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,EAAE,sBAAsB,CAAC,CAAC;IAClG,IAAI,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAC/B,IAAA,uBAAgB,EAAC,sBAAsB,EAAE,cAAc,CAAC,CAAC;IAC3D,CAAC;IACD,IAAI,kBAAkB,CAAC,GAAG,EAAE,CAAC;QAC3B,IAAA,uBAAgB,EAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;IACvD,CAAC;IACD,OAAO;QACL,WAAW;QACX,SAAS,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC;QAC7B,iBAAiB,EAAE,YAAE,CAAC,IAAI,EAAE,CAAC,MAAM;QACnC,aAAa,EAAE,MAAM;QACrB,WAAW,EAAE,4BAAoB;QACjC,sBAAsB;QACtB,GAAG,MAAM;QACT,mBAAmB;QACnB,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC;QAClF,wFAAwF;QACxF,yBAAyB,EACvB,OAAO,EAAE,yBAAyB;YAClC,MAAM,EAAE,yBAAyB;YACjC,IAAA,iCAAkB,EAAC,WAAW,EAAE,SAAS,CAAC;YAC1C,KAAK;QACP,sBAAsB;QACtB,qBAAqB,EAAE,IAAA,2BAAoB,EAAC,sBAAsB,CAAC;QACnE,kBAAkB;KACnB,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,uBAAuB,CACpC,WAAmB,EACnB,eAAsC,EACtC,OAA4B;IAE5B,MAAM,WAAW,GAAG;QAClB,GAAG,4BAAoB;QACvB,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC;QAC1B,GAAG,CAAC,OAAO,EAAE,WAAW,IAAI,EAAE,CAAC;QAC/B,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,UAAU,OAAO,CAAC,IAAI,EAAE,CAAC;KAC3E,CAAC;IAEF,MAAM,qBAAqB,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,mCAA2B,CAAC,CAAC;IAClF,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAAC;QAC3E,MAAM,sBAAsB,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7D,KAAK,MAAM,IAAI,IAAI,sBAAsB,EAAE,CAAC;YAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;YAChC,IAAI,WAAW,EAAE,CAAC;gBAChB,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IAEV,OAAO,IAAA,4BAAqB,EAAC,WAAW,CAAC,CAAC;AAC5C,CAAC;AAED,KAAK,UAAU,kBAAkB,CAC/B,WAAmB,EACnB,OAA4B,EAC5B,sBAAmC;IAEnC,MAAM,OAAO,GAA8B;QACzC,OAAO,EAAE,KAAK;QACd,GAAG,EAAE,KAAK;KACX,CAAC;IACF,MAAM,SAAS,GAAG,OAAO,EAAE,SAAS,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;IAC3D,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;QACjC,MAAM,eAAe,GAAG,MAAM,IAAA,6CAA2B,EACvD,WAAW,EACX,QAAQ,EACR,sBAAsB,CACvB,CAAC;QACF,OAAO,CAAC,QAAQ,CAAC,GAAG,eAAe,KAAK,SAAS,CAAC;IACpD,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC"}