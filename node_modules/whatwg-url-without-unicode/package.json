{"name": "whatwg-url-without-unicode", "version": "8.0.0-3", "description": "An implementation of the WHATWG URL Standard's URL API and parsing machinery, without unicode support", "main": "index.js", "files": ["index.js", "webidl2js-wrapper.js", "lib/"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "charpeni/whatwg-url", "dependencies": {"buffer": "^5.4.3", "punycode": "^2.1.1", "webidl-conversions": "^5.0.0"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.8.7", "@babel/plugin-transform-new-target": "^7.8.3", "browserify": "^16.5.0", "domexception": "^2.0.1", "eslint": "^6.8.0", "got": "^10.2.1", "jest": "^24.9.0", "recast": "^0.18.5", "webidl2js": "^12.0.0"}, "engines": {"node": ">=10"}, "scripts": {"build": "node scripts/transform.js && node scripts/convert-idl.js && babel lib/URL.js lib/URLSearchParams.js -d lib", "coverage": "jest --coverage", "lint": "eslint .", "prepare": "npm run build", "pretest": "node scripts/get-latest-platform-tests.js && npm run build", "build-live-viewer": "browserify index.js --standalone whatwgURL > live-viewer/whatwg-url.js", "test": "jest"}, "jest": {"collectCoverageFrom": ["lib/**/*.js", "!lib/utils.js"], "coverageDirectory": "coverage", "coverageReporters": ["lcov", "text-summary"], "testEnvironment": "node", "testMatch": ["<rootDir>/test/**/*.js"], "testPathIgnorePatterns": ["^<rootDir>/test/testharness.js$", "^<rootDir>/test/web-platform-tests/"]}}