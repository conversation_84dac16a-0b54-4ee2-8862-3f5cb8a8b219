/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @flow strict-local
 * @format
 */

declare const DevToolsSettingsManager: {
  setConsolePatchSettings(newConsolePatchSettings: string): void,
  getConsolePatchSettings(): ?string,
  setProfilingSettings(newProfilingSettings: string): void,
  getProfilingSettings(): ?string,
  reload(): void,
};

module.exports = DevToolsSettingsManager;
