/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @format
 * @flow strict
 */

import type {ArrayLike} from './ArrayLikeUtils';
import type DOMRectReadOnly from '../Geometry/DOMRectReadOnly';

declare export default class DOMRectList
  implements Iterable<DOMRectReadOnly>, ArrayLike<DOMRectReadOnly>
{
  // This property should've been read-only as well, but Flow doesn't handle
  // read-only indexers correctly (thinks reads are writes and fails).
  [index: number]: DOMRectReadOnly;
  +length: number;
  item(index: number): DOMRectReadOnly | null;
  @@iterator(): Iterator<DOMRectReadOnly>;
}

declare export function createDOMRectList(
  domRects: $ReadOnlyArray<DOMRectReadOnly>,
): DOMRectList;
