/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// @generated by enums.py
// clang-format off
#pragma once

#include <cstdint>
#include <yoga/YGEnums.h>
#include <yoga/enums/YogaEnums.h>

namespace facebook::yoga {

enum class PositionType : uint8_t {
  Static = YGPositionTypeStatic,
  Relative = YGPositionTypeRelative,
  Absolute = YGPositionTypeAbsolute,
};

template <>
constexpr int32_t ordinalCount<PositionType>() {
  return 3;
}

constexpr PositionType scopedEnum(YGPositionType unscoped) {
  return static_cast<PositionType>(unscoped);
}

constexpr YGPositionType unscopedEnum(PositionType scoped) {
  return static_cast<YGPositionType>(scoped);
}

inline const char* toString(PositionType e) {
  return YGPositionTypeToString(unscopedEnum(e));
}

} // namespace facebook::yoga
