# eslint-plugin-eslint-comments

[![npm version](https://img.shields.io/npm/v/eslint-plugin-eslint-comments.svg)](https://www.npmjs.com/package/eslint-plugin-eslint-comments)
[![Downloads/month](https://img.shields.io/npm/dm/eslint-plugin-eslint-comments.svg)](http://www.npmtrends.com/eslint-plugin-eslint-comments)
[![Build Status](https://github.com/mysticatea/eslint-plugin-eslint-comments/workflows/CI/badge.svg)](https://github.com/mysticatea/eslint-plugin-eslint-comments/actions)
[![codecov](https://codecov.io/gh/mysticatea/eslint-plugin-eslint-comments/branch/master/graph/badge.svg)](https://codecov.io/gh/mysticatea/eslint-plugin-eslint-comments)
[![Dependency Status](https://david-dm.org/mysticatea/eslint-plugin-eslint-comments.svg)](https://david-dm.org/mysticatea/eslint-plugin-eslint-comments)

Additional ESLint rules for ESLint directive comments (e.g. `//eslint-disable-line`).

## 📖 Usage

- [Documentation](https://mysticatea.github.io/eslint-plugin-eslint-comments)

## 🚥 Semantic Versioning Policy

`eslint-plugin-eslint-comments` follows [semantic versioning](http://semver.org/) and [ESLint's Semantic Versioning Policy](https://github.com/eslint/eslint#semantic-versioning-policy).

## 📰 Changelog

- [GitHub Releases](https://github.com/mysticatea/eslint-plugin-eslint-comments/releases)

## 🍻 Contributing

Welcome contributing!

Please use GitHub's Issues/PRs.

### Development Tools

- `npm test` runs tests and measures coverage.
- `npm run build` updates `README.md`, `index.js`, and the header of all rule's documents.
- `npm run clean` removes the coverage of the last `npm test` command.
- `npm run coverage` shows the coverage of the last `npm test` command.
- `npm run lint` runs ESLint for this codebase.
- `npm run watch` runs tests and measures coverage when source code are changed.
