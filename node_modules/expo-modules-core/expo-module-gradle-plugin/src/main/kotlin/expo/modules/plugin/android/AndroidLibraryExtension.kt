package expo.modules.plugin.android

import com.android.build.gradle.LibraryExtension

internal fun LibraryExtension.applySDKVersions(compileSdk: Int, minSdk: Int, targetSdk: Int) {
  this.compileSdk = compileSdk
  defaultConfig {
    <EMAIL> = minSdk
    <EMAIL> = targetSdk
  }
}

internal fun LibraryExtension.applyLinterOptions() {
  lintOptions.isAbortOnError = false
}

internal fun LibraryExtension.applyPublishingVariant() {
  publishing { publishing ->
    publishing.singleVariant("release") {
      withSourcesJar()
    }
  }
}
