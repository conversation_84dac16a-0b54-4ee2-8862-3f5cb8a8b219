{"ALL_COMPILER_OPTIONS_6917": "所有编译器选项", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "“{0}”修饰符不能与导入声明一起使用。", "A_0_parameter_must_be_the_first_parameter_2680": "“{0}”参数必须是第一个参数。", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "JSDoc \"@typedef\" 注释不能包含多个 \"@type\" 标记。", "A_bigint_literal_cannot_use_exponential_notation_1352": "BigInt 字面量中不能使用指数符号。", "A_bigint_literal_must_be_an_integer_1353": "BigInt 字面量必须是整数。", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "绑定模式参数在实现签名中不能为可选参数。", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "\"break\" 语句只能在封闭迭代或 switch 语句内使用。", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "\"break\" 语句只能跳转到封闭语句的标签。", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "类只能实现具有可选类型参数的标识符/限定名称。", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "类只能实现具有静态已知成员的对象类型或对象类型的交集。", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "不带 \"default\" 修饰符的类声明必须具有名称。", "A_class_member_cannot_have_the_0_keyword_1248": "类成员不可具有“{0}”关键字。", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "计算属性名中不允许逗号表达式。", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "计算属性名无法从其包含的类型引用类型参数。", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "类属性声明中的计算属性名称必须具有简单文本类型或“唯一符号”类型。", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "方法重载中的计算属性名称必须引用文本类型或 \"unique symbol\" 类型的表达式。", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "类型文本中的计算属性名称必须引用类型为文本类型或 \"unique symbol\" 类型的表达式。", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "环境上下文中的计算属性名称必须引用类型为文本类型或 \"unique symbol\" 类型的表达式。", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "接口中的计算属性名称必须引用必须引用类型为文本类型或 \"unique symbol\" 的表达式。", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "计算属性名的类型必须为 \"string\"、\"number\"、\"symbol\" 或 \"any\"。", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "'const' 断言只能作用于枚举成员、字符串、数字、布尔值、数组或对象字面量。", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "只有使用字符串文本才能访问常数枚举成员。", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "环境上下文中的 \"const\" 初始化表达式必须为字符串、数字文本或文本枚举引用。", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "当构造函数的类扩展 \"null\" 时，它不能包含 \"super\" 调用。", "A_constructor_cannot_have_a_this_parameter_2681": "构造函数不可具有 \"this\" 参数。", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "\"continue\" 语句只能在封闭迭代语句内使用。", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "\"continue\" 语句只能跳转到封闭迭代语句的标签。", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "不能在已有的环境上下文中使用 \"declare\" 修饰符。", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "修饰器仅可修饰方法实现，而不可修饰重载。", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "\"default\" 子句在 \"switch\" 语句中只能出现一次。", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "默认导出只能在 ECMAScript-style 模块中使用。", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "默认导出必须位于文件或模块声明的顶层。", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "此上下文中不允许明确的赋值断言 \"!\"。", "A_destructuring_declaration_must_have_an_initializer_1182": "析构声明必须具有初始化表达式。", "A_dynamic_import_call_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_declarat_2712": "ES5/ES3 中的动态导入调用需要 “Promise” 构造函数。请确保对 “Promise” 构造函数进行了声明或在 “--lib” 选项中包含了 “ES2015”。", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "动态导入调用返回 “Promise”。请确保具有对 “Promise” 的声明或在 “--lib” 选项中包含了 “ES2015”。", "A_file_cannot_have_a_reference_to_itself_1006": "文件不能引用自身。", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "返回“从不”的函数不能具有可访问的终结点。", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "使用 'new' 关键字调用的函数的 'this' 类型不能为 'void'。", "A_function_whose_declared_type_is_neither_void_nor_any_must_return_a_value_2355": "其声明类型不为 \"void\" 或 \"any\" 的函数必须返回值。", "A_generator_cannot_have_a_void_type_annotation_2505": "生成器不能具有 \"void\" 类型批注。", "A_get_accessor_cannot_have_parameters_1054": "\"get\" 访问器不能具有参数。", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Get 访问器必须至少具有与 Setter 相同的可访问性", "A_get_accessor_must_return_a_value_2378": "\"get\" 访问器必须返回值。", "A_label_is_not_allowed_here_1344": "此处不允许使用 'A 标签。", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "标记的元组元素被声明为可选，并且问号位于名称之后、冒号之前，而不是位于类型之后。", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "标记的元组元素通过在名称之前(而不是类型之前)的 “...” 声明为 rest。", "A_mapped_type_may_not_declare_properties_or_methods_7061": "映射的类型可能不声明属性或方法。", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "枚举声明中的成员初始化表达式不能引用在其后声明的成员(包括在其他枚举中定义的成员)。", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "mixin 类必须具有单个 rest 参数为类型 \"any[]\" 的构造函数。", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "从包含抽象构造签名的类型变量扩展的 mixin 类也必须声明为 \"abstract\"。", "A_module_cannot_have_multiple_default_exports_2528": "一个模块不能具有多个默认导出。", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "命名空间声明必须位于与之合并的类或函数所在的相同文件内。", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "命名空间声明不能位于与之合并的类或函数前", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "命名空间声明只允许位于命名空间或模块的顶层。", "A_non_dry_build_would_build_project_0_6357": "非 -dry 生成将生成项目“{0}”", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "非 -dry 生成将删除以下文件: {0}", "A_non_dry_build_would_update_output_of_project_0_6375": "非 dry 生成将更新项目 '{0}' 的输出", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "非 dry 生成将更新项目 '{0}' 的输出的时间戳", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "只允许在函数或构造函数实现中使用参数初始化表达式。", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "不能使用 rest 参数声明参数属性。", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "只允许在构造函数实现中使用参数属性。", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "不能使用绑定模式声明参数属性。", "A_promise_must_have_a_then_method_1059": "'Promise' 必须具有 'then' 方法。", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "类型为 \"unique symbol\" 的类的属性必须同时为 \"static\" 和 \"readonly\"。", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "类型为 \"unique symbol\" 的接口或类型文本的属性必须为 \"readonly\"。", "A_required_element_cannot_follow_an_optional_element_1257": "必选元素不能位于可选元素后。", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "必选参数不能位于可选参数后。", "A_rest_element_cannot_contain_a_binding_pattern_2501": "rest 元素不能包含绑定模式。", "A_rest_element_cannot_follow_another_rest_element_1265": "rest 元素不能跟在另一个 rest 元素之后。", "A_rest_element_cannot_have_a_property_name_2566": "其余元素不能具有属性名。", "A_rest_element_cannot_have_an_initializer_1186": "rest 元素不能具有初始化表达式。", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "rest 元素必须在析构模式中位于最末。", "A_rest_element_type_must_be_an_array_type_2574": "rest 元素类型必须是数组类型。", "A_rest_parameter_cannot_be_optional_1047": "rest 参数不能为可选参数。", "A_rest_parameter_cannot_have_an_initializer_1048": "rest 参数不能具有初始化表达式。", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "rest 参数必须是参数列表中的最后一个参数。", "A_rest_parameter_must_be_of_an_array_type_2370": "rest 参数必须是数组类型。", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Rest 参数或绑定模式不可带尾随逗号。", "A_return_statement_can_only_be_used_within_a_function_body_1108": "\"return\" 语句只能在函数体中使用。", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "不能在类静态块内使用 “return” 语句。", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "一系列条目，这些条目将重新映射导入内容，以查找与 \"baseUrl\" 有关的位置。", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "\"set\" 访问器不能具有返回类型批注。", "A_set_accessor_cannot_have_an_optional_parameter_1051": "\"set\" 访问器不能具有可选参数。", "A_set_accessor_cannot_have_rest_parameter_1053": "\"set\" 访问器不能具有 rest 参数。", "A_set_accessor_must_have_exactly_one_parameter_1049": "\"set\" 访问器必须正好具有一个参数。", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "\"set\" 访问器参数不能包含初始化表达式。", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "扩张参数必须具有元组类型或传递给 rest 参数。", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "“super” 调用必须是包含初始化属性、参数属性或专用标识符的派生类的构造函数中的根级语句。", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "当派生类包含初始化属性、参数属性或专用标识符时，“super” 调用必须是构造函数中用来引用 “super” 或 “this” 的第一个语句。", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "基于 \"this\" 的类型防护与基于参数的类型防护不兼容。", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "\"this\" 类型仅在类或接口的非静态成员中可用。", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "已在“{0}”中定义了 \"tsconfig.json\" 文件。", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "元组成员不能既是可选的又是 rest。", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "不能使用负值为元组类型编制索引。", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "乘方表达式的左侧不允许出现类型断言表达式。请考虑用括号将表达式括起。", "A_type_literal_property_cannot_have_an_initializer_1247": "类型文字数据不可具有初始化表达式。", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "仅类型导入可以指定默认导入或命名绑定，但不能同时指定这两者。", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "类型谓词无法引用 rest 参数。", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "类型谓词无法在绑定模式中引用元素“{0}”。", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "只允许在函数和方法的返回类型位置使用类型谓词。", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "类型谓词的类型不可赋给其参数的类型。", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "启用 “isolatedModules” 和 “emitDecoratorMetadata” 时，必须使用 “import type” 或命名空间导入来导入修饰签名中引用的类型。", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "类型为 \"unique symbol\" 的变量必须为 \"const\"。", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "只允许在生成器正文中使用 \"yield\" 表达式。", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "无法通过 super 表达式访问“{1}”类中的“{0}”抽象方法。", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "抽象方法只能出现在抽象类中。", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "不能在构造函数中访问类“{1}”中的抽象属性“{0}”。", "Accessibility_modifier_already_seen_1028": "已看到可访问性修饰符。", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "访问器仅在面向 ECMAScript 5 和更高版本时可用。", "Accessors_must_both_be_abstract_or_non_abstract_2676": "两个取值函数必须都是抽象的或都是非抽象的。", "Add_0_to_unresolved_variable_90008": "将“{0}.”添加到未解析的变量", "Add_a_return_statement_95111": "添加 return 语句", "Add_all_missing_async_modifiers_95041": "添加所有缺失的 \"async\" 修饰符", "Add_all_missing_attributes_95168": "添加所有缺少的属性", "Add_all_missing_call_parentheses_95068": "添加所有缺失的调用括号", "Add_all_missing_function_declarations_95157": "添加所有缺少的函数声明", "Add_all_missing_imports_95064": "添加所有缺少的导入", "Add_all_missing_members_95022": "添加所有缺少的成员", "Add_all_missing_override_modifiers_95162": "添加所有缺失的 \"override\" 修饰符", "Add_all_missing_properties_95166": "添加所有缺少的属性", "Add_all_missing_return_statement_95114": "添加所有缺少的 return 语句", "Add_all_missing_super_calls_95039": "添加所有缺失的 super() 调用", "Add_async_modifier_to_containing_function_90029": "将异步修饰符添加到包含函数", "Add_await_95083": "添加 \"await\"", "Add_await_to_initializer_for_0_95084": "将 \"await\" 添加到 \"{0}\" 的初始值设定项", "Add_await_to_initializers_95089": "将 \"await\" 添加到初始值设定项", "Add_braces_to_arrow_function_95059": "向箭头函数添加大括号", "Add_const_to_all_unresolved_variables_95082": "将 \"const\" 添加到所有未解析变量", "Add_const_to_unresolved_variable_95081": "将 \"const\" 添加到未解析的变量", "Add_definite_assignment_assertion_to_property_0_95020": "向属性“{0}”添加明确的赋值断言", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "将明确赋值断言添加到未初始化的所有属性", "Add_export_to_make_this_file_into_a_module_95097": "添加 \"export {}\"，将此文件变为模块", "Add_extends_constraint_2211": "添加 `extends` 约束。", "Add_extends_constraint_to_all_type_parameters_2212": "将 `extends` 约束添加到所有类型参数", "Add_import_from_0_90057": "从“{0}”添加导入", "Add_index_signature_for_property_0_90017": "为属性“{0}”添加索引签名", "Add_initializer_to_property_0_95019": "向属性“{0}”添加初始值设定项", "Add_initializers_to_all_uninitialized_properties_95027": "将初始化表达式添加到未初始化的所有属性", "Add_missing_attributes_95167": "添加缺少的属性", "Add_missing_call_parentheses_95067": "添加缺失的调用括号", "Add_missing_enum_member_0_95063": "添加缺少的枚举成员 \"{0}\"", "Add_missing_function_declaration_0_95156": "添加缺少的函数声明 \"{0}\"", "Add_missing_new_operator_to_all_calls_95072": "将缺少的 \"new\" 运算符添加到所有调用", "Add_missing_new_operator_to_call_95071": "将缺少的 \"new\" 运算符添加到调用", "Add_missing_properties_95165": "添加缺少的属性", "Add_missing_super_call_90001": "添加缺失的 \"super()\" 调用", "Add_missing_typeof_95052": "添加缺少的 \"typeof\"", "Add_names_to_all_parameters_without_names_95073": "为没有名称的所有参数添加名称", "Add_or_remove_braces_in_an_arrow_function_95058": "添加或删除箭头函数中的大括号", "Add_override_modifier_95160": "添加 \"override\" 修饰符", "Add_parameter_name_90034": "添加参数名称", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "将限定符添加到匹配成员名称的所有未解析变量", "Add_to_all_uncalled_decorators_95044": "将 \"()\" 添加到所有未调用的修饰器", "Add_ts_ignore_to_all_error_messages_95042": "将 \"@ts-ignore\" 添加到所有错误消息", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "使用索引访问时，将 “undefined” 添加到类型。", "Add_undefined_to_optional_property_type_95169": "将 “undefined” 添加到可选属性类型", "Add_undefined_type_to_all_uninitialized_properties_95029": "将未定义的类型添加到未初始化的所有属性", "Add_undefined_type_to_property_0_95018": "向属性“{0}”添加 \"undefined\" 类型", "Add_unknown_conversion_for_non_overlapping_types_95069": "为非重叠类型添加 \"unknown\" 转换", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "将 \"unknown\" 添加到非重叠类型的所有转换", "Add_void_to_Promise_resolved_without_a_value_95143": "将 \"void\" 添加到已解析但没有值的 Promise", "Add_void_to_all_Promises_resolved_without_a_value_95144": "将 \"void\" 添加到所有已解析但没有值的 Promise", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "添加 tsconfig.json 文件有助于组织包含 TypeScript 和 JavaScript 文件的项目。有关详细信息，请访问 https://aka.ms/tsconfig。", "All_declarations_of_0_must_have_identical_constraints_2838": "\"{0}\" 的所有声明必须具有相同的限制。", "All_declarations_of_0_must_have_identical_modifiers_2687": "“{0}”的所有声明必须具有相同的修饰符。", "All_declarations_of_0_must_have_identical_type_parameters_2428": "“{0}”的所有声明都必须具有相同的类型参数。", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "抽象方法的所有声明必须是连续的。", "All_destructured_elements_are_unused_6198": "所有解构出的成员都未使用。", "All_imports_in_import_declaration_are_unused_6192": "导入声明中的所有导入都未使用。", "All_type_parameters_are_unused_6205": "未使用任何类型参数。", "All_variables_are_unused_6199": "所有变量均未使用。", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "允许 JavaScript 文件成为程序的一部分。使用 “checkJS” 选项从这些文件中获取错误。", "Allow_accessing_UMD_globals_from_modules_6602": "允许从模块访问 UMD 变量全局。", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "允许从不带默认输出的模块中默认输入。这不会影响代码发出，只是类型检查。", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "当模块没有默认导出时，允许“从 y 导入 x”。", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "允许每个项目从 tslib 导入帮助程序函数一次，而不是将它们包含在每个文件中。", "Allow_javascript_files_to_be_compiled_6102": "允许编译 JavaScript 文件。", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "允许在解析模块时将多个文件夹视为一个文件夹。", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "已包含的文件名 \"{0}\" 仅大小写与文件名 \"{1}\" 不同。", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "环境模块声明无法指定相对模块名。", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "环境模块不能嵌套在其他模块或命名空间中。", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "AMD 模块无法拥有多个名称分配。", "An_abstract_accessor_cannot_have_an_implementation_1318": "抽象访问器不能有实现。", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "可访问性修饰符不能与专用标识符一起使用。", "An_accessor_cannot_have_type_parameters_1094": "访问器不能具有类型参数。", "An_accessor_property_cannot_be_declared_optional_1276": "\"accessor\" 属性不能声明为可选。", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "只允许在文件的顶层中使用环境模块声明。", "An_argument_for_0_was_not_provided_6210": "未提供 \"{0}\" 的自变量。", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "未提供与此绑定模式匹配的自变量。", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "算术操作数必须为类型 \"any\"、\"number\"、\"bigint\" 或枚举类型。", "An_arrow_function_cannot_have_a_this_parameter_2730": "箭头函数不能包含 \"this\" 参数。", "An_async_function_or_method_in_ES5_SlashES3_requires_the_Promise_constructor_Make_sure_you_have_a_de_2705": "ES5/ES3 中的异步函数或方法需要 “Promise” 构造函数。请确保具有一个 “Promise” 构造函数的声明，或在 “--lib” 选项中包含了 “ES2015”。", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "异步函数或方法必须返回 “Promise”。请确保具有对 “Promise” 的声明或在 “--lib” 选项中包含了 “ES2015”。", "An_async_iterator_must_have_a_next_method_2519": "异步迭代器必须具有 \"next()\" 方法。", "An_element_access_expression_should_take_an_argument_1011": "元素访问表达式应采用参数。", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "不能用专用标识符命名枚举成员。", "An_enum_member_cannot_have_a_numeric_name_2452": "枚举成员不能具有数值名。", "An_enum_member_name_must_be_followed_by_a_or_1357": "枚举成员名称的后面必须跟有 \",\"、\"=\" 或 \"}\"。", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "此信息的扩展版本，显示所有可能的编译器选项", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "不能在具有其他导出元素的模块中使用导出分配。", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "不能在命名空间中使用导出分配。", "An_export_assignment_cannot_have_modifiers_1120": "导出分配不能具有修饰符。", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "导出分配必须位于文件或模块声明的顶层。", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "导出声明只能在模块的顶层使用。", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "导出声明只能在命名空间或模块的顶层使用。", "An_export_declaration_cannot_have_modifiers_1193": "导出声明不能有修饰符。", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "无法测试 \"void\" 类型的表达式的真实性。", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "扩展的 Unicode 转义值必须介于(含) 0x0 和 0x10FFFF 之间。", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "标识符或关键字不能紧跟在数字字面量之后。", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "不能在环境上下文中声明实现。", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "导入别名不能引用使用 \"export type\" 导出的声明。", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "导入别名不能引用使用 \"import type\" 导入的声明。", "An_import_alias_cannot_use_import_type_1392": "导入别名不能使用“导入类型”", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "导入声明只能在模块的顶层使用。", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "导入声明只能在命名空间或模块的顶层使用。", "An_import_declaration_cannot_have_modifiers_1191": "导入声明不能有修饰符。", "An_import_path_cannot_end_with_a_0_extension_Consider_importing_1_instead_2691": "导入路径不能以“{0}”扩展名结束。考虑改为导入“{1}”。", "An_index_signature_cannot_have_a_rest_parameter_1017": "索引签名不能包含 rest 参数。", "An_index_signature_cannot_have_a_trailing_comma_1025": "索引签名不能包含尾随逗号。", "An_index_signature_must_have_a_type_annotation_1021": "索引签名必须具有类型批注。", "An_index_signature_must_have_exactly_one_parameter_1096": "索引签名必须正好具有一个参数。", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "索引签名参数不能包含问号。", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "索引签名参数不能具有可访问性修饰符。", "An_index_signature_parameter_cannot_have_an_initializer_1020": "索引签名参数不能具有初始化表达式。", "An_index_signature_parameter_must_have_a_type_annotation_1022": "索引签名参数必须具有类型批注。", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "索引签名参数类型不能为文本类型或泛型类型。请考虑改用映射的对象类型。", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "索引签名参数类型必须是 “string”、“number”、“symbol”或模板文本类型。", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "实例化表达式不能后跟属性访问。", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "接口只能扩展具有可选类型参数的标识符/限定名称。", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "接口只能扩展使用静态已知成员的对象类型或对象类型的交集。", "An_interface_cannot_extend_a_primitive_type_like_0_an_interface_can_only_extend_named_types_and_clas_2840": "接口无法扩展基元类型，如“{0}”；接口只能扩展命名类型和类", "An_interface_property_cannot_have_an_initializer_1246": "接口函数不能具有初始化表达式。", "An_iterator_must_have_a_next_method_2489": "迭代器必须具有 \"next()\" 方法。", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "将 @jsx 杂注与 JSX 片段一起使用时，需要使用 @jsxFrag 杂注。", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "对象文字不能具有多个具有相同名称的 get/set 访问器。", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "对象文本不能具有多个名称相同的属性。", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "对象文字不能包含具有相同名称的属性和访问器。", "An_object_member_cannot_be_declared_optional_1162": "对象成员无法声明为可选。", "An_optional_chain_cannot_contain_private_identifiers_18030": "可选链不能包含专用标识符。", "An_optional_element_cannot_follow_a_rest_element_1266": "可选元素不能跟在 rest 元素之后。", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "此容器隐藏了 \"this\" 的外部值。", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "重载签名无法声明为生成器。", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "乘方表达式的左侧不允许存在具有“{0}”运算符的一元表达式。请考虑用括号将表达式括起。", "Annotate_everything_with_types_from_JSDoc_95043": "使用 JSDoc 中的类型批注所有内容", "Annotate_with_type_from_JSDoc_95009": "通过 JSDoc 类型批注", "Another_export_default_is_here_2753": "这里是其他导出默认值。", "Are_you_missing_a_semicolon_2734": "是否缺少分号?", "Argument_expression_expected_1135": "应为参数表达式。", "Argument_for_0_option_must_be_Colon_1_6046": "“{0}”选项的参数必须为 {1}。", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "动态导入的参数不能是扩展元素。", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "类型“{0}”的参数不能赋给类型“{1}”的参数。", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "类型为“{0}”的参数不能分配给类型为“{1}”且 “exactOptionalPropertyTypes: true” 的参数。请考虑将 “undefined” 添加到目标属性的类型。", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "未提供 rest 形参“{0}”的实参。", "Array_element_destructuring_pattern_expected_1181": "应为数组元素析构模式。", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "断言要求使用显式类型注释声明调用目标中的每个名称。", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "断言要求调用目标为标识符或限定名。", "Asterisk_Slash_expected_1010": "应为 \"*/\"。", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "全局范围的扩大仅可直接嵌套在外部模块中或环境模块声明中。", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "全局范围的扩大应具有 \"declare\" 修饰符，除非它们显示在已有的环境上下文中。", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "项目“{0}”中启用了键入内容的自动发现。使用缓存位置“{2}”运行模块“{1}”的额外解决传递。", "Await_expression_cannot_be_used_inside_a_class_static_block_18037": "无法在类静态块内使用 await 表达式。", "BUILD_OPTIONS_6919": "生成选项", "Backwards_Compatibility_6253": "向后兼容性", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "基类表达式无法引用类类型参数。", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "基构造函数返回类型 \"{0}\" 不是具有静态已知成员的对象类型或对象类型的交集。", "Base_constructors_must_all_have_the_same_return_type_2510": "所有的基构造函数必须具有相同的返回类型。", "Base_directory_to_resolve_non_absolute_module_names_6083": "用于解析非绝对模块名的基目录。", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "目标低于 ES2020 时，BigInt 字面量不可用。", "Binary_digit_expected_1177": "需要二进制数字。", "Binding_element_0_implicitly_has_an_1_type_7031": "绑定元素“{0}”隐式具有“{1}”类型。", "Block_scoped_variable_0_used_before_its_declaration_2448": "声明之前已使用的块范围变量“{0}”。", "Build_a_composite_project_in_the_working_directory_6925": "在工作目录中生成复合项目。", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "生成所有项目，包括那些似乎是最新的项目。", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "生成一个或多个项目及其依赖项(如果已过期)", "Build_option_0_requires_a_value_of_type_1_5073": "生成选项 \"{0}\" 需要类型 {1} 的值。", "Building_project_0_6358": "正在生成项目“{0}”...", "COMMAND_LINE_FLAGS_6921": "命令行标记", "COMMON_COMMANDS_6916": "常见命令", "COMMON_COMPILER_OPTIONS_6920": "常见编译器选项", "Call_decorator_expression_90028": "调用修饰器表达式", "Call_signature_return_types_0_and_1_are_incompatible_2202": "调用签名返回类型 \"{0}\" 和 \"{1}\" 不兼容。", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "缺少返回类型批注的调用签名隐式具有返回类型 \"any\"。", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "没有参数的调用签名具有不兼容的返回类型 \"{0}\" 和 \"{1}\"。", "Call_target_does_not_contain_any_signatures_2346": "调用目标不包含任何签名。", "Can_only_convert_logical_AND_access_chains_95142": "仅可转换逻辑 AND 访问链", "Can_only_convert_named_export_95164": "只能转换已命名的导出", "Can_only_convert_property_with_modifier_95137": "只能转换带修饰符的属性", "Can_only_convert_string_concatenation_95154": "只能转换字符串串联", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "无法访问“{0}.{1}”，因为“{0}”是类型，不是命名空间。是否要使用“{0}[\"{1}\"]”检索“{0}”中“{1}”属性的类型?", "Cannot_access_ambient_const_enums_when_the_isolatedModules_flag_is_provided_2748": "提供 \"--isolatedModules\" 标志时，无法访问环境常量枚举。", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "不可将“{0}”构造函数类型分配给“{1}”构造函数类型。", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "无法将抽象构造函数类型分配给非抽象构造函数类型。", "Cannot_assign_to_0_because_it_is_a_class_2629": "无法为“{0}”赋值，因为它是类。", "Cannot_assign_to_0_because_it_is_a_constant_2588": "无法分配到 \"{0}\" ，因为它是常数。", "Cannot_assign_to_0_because_it_is_a_function_2630": "无法为“{0}”赋值，因为它是函数。", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "无法为“{0}”赋值，因为它是命名空间。", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "无法为“{0}”赋值，因为它是只读属性。", "Cannot_assign_to_0_because_it_is_an_enum_2628": "无法为“{0}”赋值，因为它是枚举。", "Cannot_assign_to_0_because_it_is_an_import_2632": "无法为“{0}”赋值，因为它是导入。", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "无法为“{0}”赋值，因为它不是变量。", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "无法赋值给私有方法“{0}”。私有方法不可写。", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "无法扩大模块“{0}”，因为它解析为非模块实体。", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "无法扩充具有值导出的模块“{0}”，因为它解析为一个非模块的实体。", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "无法使用选项“{0}”来编译模块，除非 \"--module\" 标记为 \"amd\" 或 \"system\"。", "Cannot_create_an_instance_of_an_abstract_class_2511": "无法创建抽象类的实例。", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "无法将迭代委托到值，因为其迭代器的 \"next\" 方法需要类型 \"{1}\"，但包含它的生成器将始终发送 \"{0}\"。", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "无法导出“{0}”。仅可从模块中导出本地声明。", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "无法扩展类“{0}”。类构造函数标记为私有。", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "无法扩展接口“{0}”。您是否想使用 \"implements\"?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "无法在当前目录找到 tsconfig.json 文件: {0}。", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "无法在指定目录找到 tsconfig.json 文件:“{0}”。", "Cannot_find_global_type_0_2318": "找不到全局类型“{0}”。", "Cannot_find_global_value_0_2468": "找不到全局值“{0}”。", "Cannot_find_lib_definition_for_0_2726": "找不到“{0}”的库定义。", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "找不到“{0}”的库定义。你是指“{1}”?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "找不到模块“{0}”。请考虑使用 \"--resolveJsonModule\" 导入带 \".json\" 扩展的模块。", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_node_or_to_add_aliases_to_th_2792": "找不到模块“{0}”。你的意思是要将 \"moduleResolution\" 选项设置为 \"node\"，还是要将别名添加到 \"paths\" 选项中?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "找不到模块“{0}”或其相应的类型声明。", "Cannot_find_name_0_2304": "找不到名称“{0}”。", "Cannot_find_name_0_Did_you_mean_1_2552": "找不到名称“{0}”。你是否指的是“{1}”?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "找不到名称“{0}”。你的意思是实例成员“this.{0}”?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "找不到名称“{0}”。你的意思是静态成员“{1}.{0}”?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "找不到名称“{0}”。你是否要在异步函数中写入此内容?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "找不到名称“{0}”。是否需要更改目标库? 请尝试将 “lib” 编译器选项更改为“{1}”或更高版本。", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "找不到名称“{0}”。是否需要更改目标库? 请尝试更改 “lib” 编译器选项以包括 “dom”。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "找不到名称 \"{0}\"。是否需要安装测试运行器的类型定义? 请尝试使用 `npm i --save-dev @types/jest` 或 `npm i --save-dev @types/mocha`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "找不到名称“{0}”。是否需要安装测试运行器的类型定义? 请尝试使用 `npm i --save-dev @types/jest` 或 `npm i --save-dev @types/mocha`，然后将 “jest” 或 “mocha” 添加到 tsconfig 中的类型字段。。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "找不到名称 \"{0}\"。是否需要安装 jQuery 的类型定义? 请尝试使用 `npm i --save-dev @types/jquery`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "找不到名称“{0}”。是否需要安装 jQuery 的类型定义? 请尝试使用 `npm i --save-dev @types/jquery`，然后将 “jquery” 添加到 teconfig 中的类型字段。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "找不到名称“{0}”。是否需要安装 Node.js 的类型定义? 请尝试运行 `npm i --save-dev @types/node`。", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "找不到名称“{0}”。是否需要安装 Node.js 的类型定义? 请尝试运行 `npm i --save-dev @types/node`，然后将 \"node\" 添加到 tsconfig 的 types 字段。", "Cannot_find_namespace_0_2503": "找不到命名空间“{0}”。", "Cannot_find_namespace_0_Did_you_mean_1_2833": "找不到命名空间“{0}”。你是否指的是“{1}”?", "Cannot_find_parameter_0_1225": "找不到参数“{0}”。", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "找不到输入文件的公共子目录路径。", "Cannot_find_type_definition_file_for_0_2688": "找不到“{0}”的类型定义文件。", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "无法导入类型声明文件。请考虑导入“{0}”，而不是“{1}”。", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "无法在块范围声明“{1}”所在的范围内初始化外部范围变量“{0}”。", "Cannot_invoke_an_object_which_is_possibly_null_2721": "不能调用可能是 \"null\" 的对象。", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "不能调用可能是 \"null\" 或“未定义”的对象。", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "不能调用可能是“未定义”的对象。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "无法迭代值，因为其迭代器的 \"next\" 方法需要类型 \"{1}\"，但数组析构将始终发送 \"{0}\"。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "无法迭代值，因为其迭代器的 \"next\" 方法需要类型 \"{1}\"，但数组扩张将始终发送 \"{0}\"。", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "无法迭代值，因为其迭代器的 \"next\" 方法需要类型 \"{1}\"，但 for-of 将始终发送 \"{0}\"。", "Cannot_prepend_project_0_because_it_does_not_have_outFile_set_6308": "无法为项目“{0}”添加前缀，因为它未设置 \"outFile\"", "Cannot_read_file_0_5083": "无法读取文件“{0}”。", "Cannot_read_file_0_Colon_1_5012": "无法读取文件“{0}”: {1}。", "Cannot_redeclare_block_scoped_variable_0_2451": "无法重新声明块范围变量“{0}”。", "Cannot_redeclare_exported_variable_0_2323": "无法重新声明导出的变量“{0}”。", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "无法在 catch 子句中重新声明标识符“{0}”。", "Cannot_start_a_function_call_in_a_type_annotation_1441": "无法在类型注释中启动函数调用。", "Cannot_update_output_of_project_0_because_there_was_error_reading_file_1_6376": "无法更新项目 \"{0}\" 的输出，因为读取文件 \"{1}\" 时出错", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "无法使用 JSX，除非提供了 \"--jsx\" 标志。", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_the_isolatedModules_flag_is_provided_1269": "在提供 “--isolatedModules” 标志时，无法在类型或仅类型命名空间上使用“导出导入”。", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "当 \"--module\" 为 \"none\" 时无法使用导入、导出或模块扩大。", "Cannot_use_namespace_0_as_a_type_2709": "不能将命名空间“{0}”用作类型。", "Cannot_use_namespace_0_as_a_value_2708": "不能将命名空间“{0}”用作值。", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "无法在修饰类静态属性初始化表达式中使用 “this”。", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "无法写入文件 \"{0}\"，因为它将覆盖由引用的项目 \"{1}\" 生成的 \".tsbuildinfo\" 文件", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "无法写入文件“{0}”，因为它会被多个输入文件覆盖。", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "无法写入文件“{0}”，因为它会覆盖输入文件。", "Catch_clause_variable_cannot_have_an_initializer_1197": "Catch 子句变量不能有初始化表达式。", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "Catch 子句变量类型注释必须为 \"any\" 或 \"unknown\" (若已指定)。", "Change_0_to_1_90014": "将“{0}”更改为“{1}”", "Change_all_extended_interfaces_to_implements_95038": "将所有扩展接口都更改为 \"implements\"", "Change_all_jsdoc_style_types_to_TypeScript_95030": "将所有 JSDoc 样式类型都更改为 TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "将所有 JSDoc 样式类型都更改为 TypeScript (并将 \"| undefined\" 添加到可以为 null 的类型)", "Change_extends_to_implements_90003": "将 \"extends\" 改为 \"implements\"", "Change_spelling_to_0_90022": "将拼写更改为“{0}”", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "检查是否有已声明但未在构造函数中设置的类属性。", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "检查 “bind”、“call” 和 “apply” 方法的参数是否与原始函数匹配。", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "检查“{0}”是否是“{1}”-“{2}”的最长匹配前缀。", "Circular_definition_of_import_alias_0_2303": "导入别名“{0}”的循环定义。", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "解析配置时检测到循环: {0}", "Circularity_originates_in_type_at_this_location_2751": "循环源自此位置的类型。", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "类“{0}”将“{1}”定义为实例成员访问器，但扩展类“{2}”将其定义为实例成员函数。", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "类“{0}”将“{1}”定义为实例成员函数，但扩展类“{2}”将其定义为实例成员访问器。", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "类“{0}”将“{1}”定义为实例成员属性，但扩展类“{2}”将其定义为实例成员函数。", "Class_0_incorrectly_extends_base_class_1_2415": "类“{0}”错误扩展基类“{1}”。", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "类“{0}”错误实现类“{1}”。你是想扩展“{1}”并将其成员作为子类继承吗?", "Class_0_incorrectly_implements_interface_1_2420": "类“{0}”错误实现接口“{1}”。", "Class_0_used_before_its_declaration_2449": "类“{0}”用于其声明前。", "Class_constructor_may_not_be_a_generator_1368": "类构造函数可能不是生成器。", "Class_constructor_may_not_be_an_accessor_1341": "类构造函数可能不是访问器。", "Class_declaration_cannot_implement_overload_list_for_0_2813": "类声明无法实现“{0}”的重载列表。", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "类声明不能有多个 “@augments” 或 “@extends” 标记。", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "类修饰器不能与静态专用标识符一起使用。请考虑删除实验性修饰器。", "Class_name_cannot_be_0_2414": "类名不能为“{0}”。", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "使用模块 {0} 将目标设置为 ES5 时，类名称不能为 \"Object\"。", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "类静态侧“{0}”错误扩展基类静态侧“{1}”。", "Classes_can_only_extend_a_single_class_1174": "类只能扩展一个类。", "Classes_may_not_have_a_field_named_constructor_18006": "类不能具有名为 \"constructor\" 的字段。", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "类中包含的代码在 JavaScript 的严格模式下进行计算，该模式不允许以此方式使用“{0}”。有关详细信息，请参阅 https://developer.mozilla.org/zh-CN/docs/Web/JavaScript/Reference/Strict_mode。", "Command_line_Options_6171": "命令行选项", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "编译给定了其配置文件路径或带 \"tsconfig.json\" 的文件夹路径的项目。", "Compiler_Diagnostics_6251": "编译器诊断", "Compiler_option_0_expects_an_argument_6044": "编译器选项“{0}”需要参数。", "Compiler_option_0_may_not_be_used_with_build_5094": "编译器选项“--{0}”不能与 “--build” 一起使用。", "Compiler_option_0_may_only_be_used_with_build_5093": "编译器选项“--{0}”只能与 “--build” 一起使用。", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "值“{1}”的编译器选项“{0}”不稳定。使用夜间 TypeScript 消除此错误。请尝试使用 “npm install -D typescript@next” 进行更新。", "Compiler_option_0_requires_a_value_of_type_1_5024": "编译器选项“{0}”需要类型 {1} 的值。", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "当发出专用标识符下层时，编译器会预留名称“{0}”。", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "编译位于指定路径的 TypeScript 项目。", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "编译当前项目(工作目录中的 tsconfig.json。)", "Compiles_the_current_project_with_additional_settings_6929": "使用其他设置编译当前项目。", "Completeness_6257": "完成度", "Composite_projects_may_not_disable_declaration_emit_6304": "复合项目可能不会禁用声明发出。", "Composite_projects_may_not_disable_incremental_compilation_6379": "复合项目不能禁用增量编译。", "Computed_from_the_list_of_input_files_6911": "从输入文件列表计算", "Computed_property_names_are_not_allowed_in_enums_1164": "枚举中不允许计算属性名。", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "含字符串值成员的枚举中不允许使用计算值。", "Concatenate_and_emit_output_to_single_file_6001": "连接输出并将其发出到单个文件。", "Conflicting_definitions_for_0_found_at_1_and_2_Consider_installing_a_specific_version_of_this_librar_4090": "“{1}”和“{2}”处找到的“{0}”的定义具有冲突。考虑安装此库的特定版本以解决冲突。", "Conflicts_are_in_this_file_6201": "此文件中存在冲突。", "Consider_adding_a_declare_modifier_to_this_class_6506": "请考虑向此类添加 “declare” 修饰符。", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "构造签名返回类型 \"{0}\" 和 \"{1}\" 不兼容。", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "缺少返回类型批注的构造签名隐式具有返回类型 \"any\"。", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "不带参数的构造签名具有不兼容的返回类型 \"{0}\" 和 \"{1}\"。", "Constructor_implementation_is_missing_2390": "缺少构造函数实现。", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "类“{0}”的构造函数是私有的，仅可在类声明中访问。", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "类“{0}”的构造函数是受保护的，仅可在类声明中访问。", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "在联合类型中使用时，构造函数类型标记必须用括号括起来。", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "在相交类型中使用时，构造函数类型标记必须用括号括起来。", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "派生类的构造函数必须包含 \"super\" 调用。", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "未指定包含文件，并且无法确定根目录，正在跳过在 \"node_modules\" 文件夹中查找。", "Containing_function_is_not_an_arrow_function_95128": "包含函数不是箭头函数", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "控制用于检测模块格式 JS 文件的方法。", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "类型 \"{0}\" 到类型 \"{1}\" 的转换可能是错误的，因为两种类型不能充分重叠。如果这是有意的，请先将表达式转换为 \"unknown\"。", "Convert_0_to_1_in_0_95003": "将“{0}”转换为 {0} 中的 {1}", "Convert_0_to_mapped_object_type_95055": "将“{0}”转换为映射对象类型", "Convert_all_const_to_let_95102": "将所有 'const' 转换为 'let'", "Convert_all_constructor_functions_to_classes_95045": "将所有构造函数都转换为类", "Convert_all_imports_not_used_as_a_value_to_type_only_imports_1374": "将不用作值的所有导入转换为仅类型导入", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "将所有无效字符转换为 HTML 实体代码", "Convert_all_re_exported_types_to_type_only_exports_1365": "将所有重新导出的类型转换为仅类型导出", "Convert_all_require_to_import_95048": "将所有 \"require\" 转换为 \"import\"", "Convert_all_to_async_functions_95066": "全部转换为异步函数", "Convert_all_to_bigint_numeric_literals_95092": "全部转换为 BigInt 数字字面量", "Convert_all_to_default_imports_95035": "全部转换为默认导入", "Convert_all_type_literals_to_mapped_type_95021": "将所有类型文本转换为映射类型", "Convert_arrow_function_or_function_expression_95122": "转换箭头函数或函数表达式", "Convert_const_to_let_95093": "将 \"const\" 转换为 \"let\"", "Convert_default_export_to_named_export_95061": "将默认导出转换为命名导出", "Convert_function_declaration_0_to_arrow_function_95106": "将函数声明“{0}”转换为箭头函数", "Convert_function_expression_0_to_arrow_function_95105": "将函数表达式 \"{0}\" 转换为箭头函数", "Convert_function_to_an_ES2015_class_95001": "将函数转换为 ES2015 类", "Convert_invalid_character_to_its_html_entity_code_95100": "将无效字符转换为其 HTML 实体代码", "Convert_named_export_to_default_export_95062": "将命名导出转换为默认导出", "Convert_named_imports_to_default_import_95170": "将命名导入转换为默认导入", "Convert_named_imports_to_namespace_import_95057": "将命名导入转换为命名空间导入", "Convert_namespace_import_to_named_imports_95056": "将命名空间导入转换为命名导入", "Convert_overload_list_to_single_signature_95118": "将重载列表转换为单一签名", "Convert_parameters_to_destructured_object_95075": "将参数转换为析构对象", "Convert_require_to_import_95047": "将 \"require\" 转换为 \"import\"", "Convert_to_ES_module_95017": "转换为 ES 模块", "Convert_to_a_bigint_numeric_literal_95091": "转换为 BigInt 数字字面量", "Convert_to_anonymous_function_95123": "转换为异步函数", "Convert_to_arrow_function_95125": "转换为箭头函数", "Convert_to_async_function_95065": "转换为异步函数", "Convert_to_default_import_95013": "转换为默认导入", "Convert_to_named_function_95124": "转换为指定函数", "Convert_to_optional_chain_expression_95139": "转换为可选链表达式", "Convert_to_template_string_95096": "转换为模板字符串", "Convert_to_type_only_export_1364": "转换为仅类型导出", "Convert_to_type_only_import_1373": "转换为仅类型导入", "Corrupted_locale_file_0_6051": "区域设置文件 {0} 已损坏。", "Could_not_convert_to_anonymous_function_95153": "无法转换为匿名函数", "Could_not_convert_to_arrow_function_95151": "无法转换为箭头函数", "Could_not_convert_to_named_function_95152": "无法转换为命名函数", "Could_not_determine_function_return_type_95150": "无法确定函数返回类型", "Could_not_find_a_containing_arrow_function_95127": "找不到包含箭头函数", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "无法找到模块“{0}”的声明文件。“{1}”隐式拥有 \"any\" 类型。", "Could_not_find_convertible_access_expression_95140": "找不到可转换的访问表达式", "Could_not_find_export_statement_95129": "找不到 export 语句", "Could_not_find_import_clause_95131": "找不到 import 子句", "Could_not_find_matching_access_expressions_95141": "找不到匹配的访问表达式", "Could_not_find_name_0_Did_you_mean_1_2570": "找不到名称“{0}”。你是否是指“{1}”?", "Could_not_find_namespace_import_or_named_imports_95132": "找不到命名空间导入或已命名的导入", "Could_not_find_property_for_which_to_generate_accessor_95135": "找不到要为其生成访问器的属性", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "无法解析具有表达式的路径“{0}”: {1}。", "Could_not_write_file_0_Colon_1_5033": "无法写入文件“{0}”: {1}。", "Create_source_map_files_for_emitted_JavaScript_files_6694": "为发出的 JavaScript 文件创建源映射文件。", "Create_sourcemaps_for_d_ts_files_6614": "为 d.ts 文件创建源映射。", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "使用工作目录中的建议设置创建 tsconfig.json。", "DIRECTORY_6038": "目录", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "该声明扩充了另一文件中的声明。这无法被序列化。", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "此文件的声明发出要求使用专用名称 \"{0}\"。显式类型注释可能取消阻止声明发出。", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "此文件的声明发出要求使用模块 \"{1}\" 中的专用名称 \"{0}\"。显式类型注释可能取消阻止声明发出。", "Declaration_expected_1146": "应为声明。", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "声明名称与内置全局标识符“{0}”冲突。", "Declaration_or_statement_expected_1128": "应为声明或语句。", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "应为声明或语句。此 \"=\" 遵循语句块，因此如果打算编写重构赋值，则可能需要用括号将整个赋值括起来。", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "具有明确赋值断言的声明也必须具有类型批注。", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "具有初始值设定项的声明不能同时具有明确赋值断言。", "Declare_a_private_field_named_0_90053": "声明名为 \"{0}\" 的专用字段。", "Declare_method_0_90023": "声明方法“{0}”", "Declare_private_method_0_90038": "声明私有方法 \"{0}\"", "Declare_private_property_0_90035": "声明专用属性“{0}”", "Declare_property_0_90016": "声明属性“{0}”", "Declare_static_method_0_90024": "声明静态方法“{0}”", "Declare_static_property_0_90027": "声明静态属性“{0}”", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "装饰器函数返回类型“{0}”不可分配到类型“{1}”。", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "装饰器函数返回类型为“{0}”，但预期为“void”或“any”。", "Decorators_are_not_valid_here_1206": "修饰器在此处无效。", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "不能向多个同名的 get/set 访问器应用修饰器。", "Decorators_may_not_be_applied_to_this_parameters_1433": "修饰器不能应用于 “this” 参数。", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "修饰器必须位于属性声明的名称和所有关键字之前。", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "将 catch 子句变量默认为 “unknown” 而不是 “any”。", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "模块的默认导出具有或正在使用专用名称“{0}”。", "Default_library_1424": "默认库", "Default_library_for_target_0_1425": "目标 \"{0}\" 的默认库", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "以下标识符的定义与另一个文件中的定义冲突: {0}", "Delete_all_unused_declarations_95024": "删除未使用的所有声明", "Delete_all_unused_imports_95147": "删除所有未使用的导入", "Delete_all_unused_param_tags_95172": "删除所有未使用的 “@param” 标记", "Delete_the_outputs_of_all_projects_6365": "删除所有项目的输出。", "Delete_unused_param_tag_0_95171": "删除未使用的 “@param” 标记“{0}”", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[已弃用] 请改用 \"--jsxFactory\"。已 \"react\" JSX 发出设为目标时，请指定要为 createElement 调用的对象", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[已弃用] 请改用 \"--outFile\"。连接并发出到单个文件的输出", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[已弃用] 请改用 \"--skipLibCheck\"。请跳过默认库声明文件的类型检查。", "Deprecated_setting_Use_outFile_instead_6677": "弃用的设置。请改用 “outFile”。", "Did_you_forget_to_use_await_2773": "是否忘记使用 \"await\"?", "Did_you_mean_0_1369": "你是想使用 \"{0}\" 吗?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "你是想将 \"{0}\" 限制为类型 \"new (...args: any[]) => {1}\" 吗?", "Did_you_mean_to_call_this_expression_6212": "你是想调用此表达式吗?", "Did_you_mean_to_mark_this_function_as_async_1356": "你是想将此函数标记为 \"async\" 吗?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "你的意思是使用 \":\" 吗? 当包含对象文字属于解构模式时，\"=\" 只能跟在属性名称的后面。", "Did_you_mean_to_use_new_with_this_expression_6213": "你是想将 \"new\" 用于此表达式吗?", "Digit_expected_1124": "应为数字。", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "目录“{0}”不存在，正在跳过该目录中的所有查找。", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "目录 '{0}' 不包含 package.json 作用域。导入将无法解析。", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "禁止在发出的 JavaScript 文件中添加 “use strict” 指令。", "Disable_checking_for_this_file_90018": "禁用检查此文件", "Disable_emitting_comments_6688": "禁用发出注释。", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "禁用在其 JSDoc 注释中包含 “@internal” 的发出声明。", "Disable_emitting_files_from_a_compilation_6660": "禁用从编译发出文件。", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "禁用在报告了任何类型检查错误时发出文件。", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "在生成的代码中禁用擦除 “const enum” 声明。", "Disable_error_reporting_for_unreachable_code_6603": "对无法访问的代码禁用错误报告。", "Disable_error_reporting_for_unused_labels_6604": "对未使用的标签禁用错误报告。", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "在已编译输出中禁用生成自定义帮助程序函数(如 “__extends”)。", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "禁用包括任何库文件(包括默认的 lib.d.ts)。", "Disable_loading_referenced_projects_6235": "禁止加载引用的项目。", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "在引用复合项目时禁用首选源文件而不是声明文件。", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "禁用在创建对象文字期间报告多余属性错误。", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "禁用将符号链接解析为其实际路径。这会关联到节点中的同一标志。", "Disable_size_limitations_on_JavaScript_projects_6162": "禁用对 JavaScript 项目的大小限制。", "Disable_solution_searching_for_this_project_6224": "对此项目禁用解决方案搜索。", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "禁止严格检查函数类型中的通用签名。", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "禁用针对 JavaScript 项目的类型获取", "Disable_truncating_types_in_error_messages_6663": "在错误消息中禁用截断类型。", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "禁止使用源文件而不是引用项目中的声明文件。", "Disable_wiping_the_console_in_watch_mode_6684": "禁用在监视模式下擦除控制台。", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "禁用通过查看项目中的文件名进行类型获取推理。", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "禁止 “import”、“require” 或 “<reference>” 扩展 TypeScript 应添加到项目的文件数。", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "不允许对同一文件采用大小不一致的引用。", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "请勿将三斜杠引用或导入的模块添加到已编译文件列表中。", "Do_not_emit_comments_to_output_6009": "请勿将注释发出到输出。", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "请勿对具有 \"@internal\" 注释的代码发出声明。", "Do_not_emit_outputs_6010": "请勿发出输出。", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "如果报告了任何错误，请不要发出输出。", "Do_not_emit_use_strict_directives_in_module_output_6112": "不要在模块输出中发出 \"use strict\" 指令。", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "请勿清除生成代码中的常数枚举声明。", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "请勿在已编译输出中生成自定义帮助程序函数，例如 \"__extends\"。", "Do_not_include_the_default_library_file_lib_d_ts_6158": "请勿包括默认库文件(lib.d.ts)。", "Do_not_report_errors_on_unreachable_code_6077": "不报告有关不可访问的代码的错误。", "Do_not_report_errors_on_unused_labels_6074": "不报告有关未使用的标签的错误。", "Do_not_resolve_the_real_path_of_symlinks_6013": "不要解析 symlink 的真实路径。", "Do_not_truncate_error_messages_6165": "请勿删除错误消息。", "Duplicate_function_implementation_2393": "函数实现重复。", "Duplicate_identifier_0_2300": "标识符“{0}”重复。", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "标识符“{0}”重复。编译器在模块的顶层范围中保留名称“{1}”。", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "标识符“{0}”重复。编译器在包含异步函数的模块的顶层范围中保留名称“{1}”。", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "标识符“{0}”重复。在静态初始化表达式中中发出 “super” 引用时，编译器保留名称“{1}”。", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "标识符“{0}”重复。编译器使用“{1}”声明来支持异步函数。", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "标识符 \"{0}\" 重复。静态元素和实例元素不能共享相同的专用名称。", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "标识符 \"arguments\" 重复。编译器使用 \"arguments\" 初始化 rest 参数。", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "标识符 \"_newTarget\" 重复。编译器使用变量声明 \"_newTarget\" 来捕获 \"new.target\" 元属性引用。", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "标识符 \"_this\" 重复。编译器使用变量声明 \"_this\" 来捕获 \"this\" 引用。", "Duplicate_index_signature_for_type_0_2374": "类型“{0}”的索引签名重复。", "Duplicate_label_0_1114": "标签“{0}”重复。", "Duplicate_property_0_2718": "重复的属性 \"{0}\"。", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "动态导入的说明符类型必须是 \"string\"，但此处类型是 \"{0}\"。", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "仅在将 “--module” 标记设置为 “es2020”、“es2022”、“esnext”、“commonjs”、“amd”、“system”、“umd”、“node16” 或 “nodenext” 时，才支持动态导入。", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_assertion_as_arguments_1450": "动态导入只能接受模块说明符和可选断言作为参数", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_or_nod_1324": "只有当 “--module” 选项设置为 “esnext”、 “node16” 或 “nodenext” 时，动态导入才支持第二个参数。", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "联合类型 \"{0}\" 的每个成员都有构造签名，但这些签名都不能互相兼容。", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "联合类型 \"{0}\" 的每个成员都有签名，但这些签名都不能互相兼容。", "Editor_Support_6249": "编辑器支持", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "元素隐式具有 \"any\" 类型，因为类型为 \"{0}\" 的表达式不能用于索引类型 \"{1}\"。", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "元素隐式具有 \"any\" 类型，因为索引表达式的类型不为 \"number\"。", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "元素隐式具有 \"any\" 类型，因为类型“{0}”没有索引签名。", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "元素隐式具有 \"any\" 类型，因为类型 \"{0}\" 没有索引签名。你是想调用 \"{1}\" 吗?", "Emit_6246": "发出", "Emit_ECMAScript_standard_compliant_class_fields_6712": "发出符合 ECMAScript 标准的类字段。", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "在输出文件的开头发出一个 UTF-8 字节顺序标记(BOM)。", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "发出包含源映射而非包含单独文件的单个文件。", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "发出用于调试的编译器运行的 v8 CPU 配置文件。", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "发出其他 JavaScript 以轻松支持导入 CommonJS 模块。这将启用 “allowSyntheticDefaultImports” 以实现类型兼容性。", "Emit_class_fields_with_Define_instead_of_Set_6222": "使用 Define 而不是 Set 发出类字段。", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "为源文件中的修饰声明发出设计类型元数据。", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "发出更合规但更详细且性能较低的 JavaScript 进行迭代。", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "在单个文件内发出源以及源映射；需要设置 \"--inlineSourceMap\" 或 \"--sourceMap\"。", "Enable_all_strict_type_checking_options_6180": "启用所有严格类型检查选项。", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "在 TypeScript 输出中启用颜色和格式设置，以使编译器错误更易于阅读。", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "启用允许将 TypeScript 项目与项目引用一起使用的约束。", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "为未在函数中显式返回的代码路径启用错误报告。", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "对具有隐式 “any” 类型的表达式和声明启用错误报告。", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "为 switch 语句中的故障案例启用错误报告。", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "在已检查类型的 JavaScript 文件中启用错误报告。", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "在未读取局部变量时启用错误报告。", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "在 “this” 的类型为 “any” 时启用错误报告。", "Enable_experimental_support_for_TC39_stage_2_draft_decorators_6630": "为 TC39 暂存 2 草稿修饰器启用实验性支持。", "Enable_importing_json_files_6689": "启用导入 .json 文件。", "Enable_project_compilation_6302": "启用项目编译", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "对函数启用严格的 \"bind\"、\"call\" 和 \"apply\" 方法。", "Enable_strict_checking_of_function_types_6186": "对函数类型启用严格检查。", "Enable_strict_checking_of_property_initialization_in_classes_6187": "启用类中属性初始化的严格检查。", "Enable_strict_null_checks_6113": "启用严格的 NULL 检查。", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "在配置文件中启用 \"experimentalDecorators\" 选项", "Enable_the_jsx_flag_in_your_configuration_file_95088": "在配置文件中启用 \"--jsx\" 标志", "Enable_tracing_of_the_name_resolution_process_6085": "启用名称解析过程的跟踪。", "Enable_verbose_logging_6713": "启用详细日志记录。", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "通过为所有导入创建命名空间对象来启用 CommonJS 和 ES 模块之间的发出互操作性。表示 \"allowSyntheticDefaultImports\"。", "Enables_experimental_support_for_ES7_decorators_6065": "对 ES7 修饰器启用实验支持。", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "对发出修饰器的类型元数据启用实验支持。", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "对使用索引类型声明的键强制使用索引访问器。", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "确保使用替代修饰符标记派生类中的替代成员。", "Ensure_that_casing_is_correct_in_imports_6637": "确保导入中的大小写正确。", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "确保可以安全地转译每个文件，而无需依赖其他导入。", "Ensure_use_strict_is_always_emitted_6605": "请确保始终发出 “se strict”。", "Entry_point_for_implicit_type_library_0_1420": "隐式类型库 \"{0}\" 的入口点", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "隐式类型库 \"{0}\" 的入口点，具有 packageId \"{1}\"", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "在 compilerOptions 中指定的类型库 \"{0}\" 的入口点", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "在 compilerOptions 中指定的类型库 \"{0}\" 的入口点，具有 packageId \"{1}\"", "Enum_0_used_before_its_declaration_2450": "枚举“{0}”用于其声明前。", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "枚举声明只能与命名空间或其他枚举声明合并。", "Enum_declarations_must_all_be_const_or_non_const_2473": "枚举声明必须全为常数或非常数。", "Enum_member_expected_1132": "应为枚举成员。", "Enum_member_must_have_initializer_1061": "枚举成员必须具有初始化表达式。", "Enum_name_cannot_be_0_2431": "枚举名不能为“{0}”。", "Enum_type_0_has_members_with_initializers_that_are_not_literals_2535": "枚举类型“{0}”包含具有不是文本的初始值设定项的成员。", "Errors_Files_6041": "错误文件", "Examples_Colon_0_6026": "示例: {0}", "Excessive_stack_depth_comparing_types_0_and_1_2321": "与类型“{0}”和“{1}”相比，堆栈深度过高。", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "应为 {0}-{1} 类型参数；请为这些参数添加 \"@extends\" 标记。", "Expected_0_arguments_but_got_1_2554": "应有 {0} 个参数，但获得 {1} 个。", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "应为 {0} 个参数，但得到的却是 {1} 个。你是否忘了将类型参数中的 \"void\" 包含到 \"Promise\"?", "Expected_0_type_arguments_but_got_1_2558": "应有 {0} 个类型参数，但获得 {1} 个。", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "应为 {0} 类型参数；请为这些参数添加 \"@extends\" 标记。", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "应为 1 个参数，但得到 0。“new Promise()” 需要 JSDoc 提示才能生成可在没有参数的情况下调用的 “resolve”。", "Expected_at_least_0_arguments_but_got_1_2555": "应有至少 {0} 个参数，但获得 {1} 个。", "Expected_corresponding_JSX_closing_tag_for_0_17002": "“{0}”预期的相应 JSX 结束标记。", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "预期的 JSX 片段的相应结束标记。", "Expected_for_property_initializer_1442": "属性初始化表达式应有 \"=\"。", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "\"package.json\" 中 \"{0}\" 字段的类型应为 \"{1}\"，但实际为 \"{2}\" 。", "Experimental_support_for_decorators_is_a_feature_that_is_subject_to_change_in_a_future_release_Set_t_1219": "对修饰器的实验支持功能在将来的版本中可能更改。在 \"tsconfig\" 或 \"jsconfig\" 中设置 \"experimentalDecorators\" 选项以删除此警告。", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "显示指定了模块解析类型:“{0}”。", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "除非 \"target\" 选项设置为 \"es2016\" 或更高版本，否则不能对 \"bigint\" 值执行求幂运算。", "Export_0_from_module_1_90059": "从模块“{1}”导出“{0}”", "Export_all_referenced_locals_90060": "导出所有引用的局部变量", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "面向 ECMAScript 模块时，不能使用导出分配。请考虑改用 \"export default\" 或另一种模块格式。", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "当 \"--module\" 标志是 \"system\" 时不支持导出分配。", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "导出声明与“{0}”的导出声明冲突。", "Export_declarations_are_not_permitted_in_a_namespace_1194": "命名空间中不允许有导出声明。", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "路径 '{1}' 处的 package.json 作用域中不存在导出说明符 '{0}'。", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "导出的类型别名“{0}”已经或正在使用专用名称“{1}”。", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "导出的类型别名“{0}”具有或正在使用模块“{2}”中的专用名称“{1}”。", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "导出的变量“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "导出的变量“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Exported_variable_0_has_or_is_using_private_name_1_4025": "导出的变量“{0}”具有或正在使用专用名称“{1}”。", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "模块扩大中不允许导出和导出分配。", "Expression_expected_1109": "应为表达式。", "Expression_or_comma_expected_1137": "应为表达式或逗号。", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "表达式生成的元组类型太大，无法表示。", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "表达式生成的联合类型过于复杂，无法表示。", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "表达式解析为 \"_super\"，编译器使用 \"_super\" 获取基类引用。", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "表达式解析为编辑器用于捕获 \"new.target\" 元属性引用的变量声明 \"_newTarget\"。", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "表达式解析为编译器用于捕获 \"this\" 引用的变量声明 \"_this\"。", "Extract_constant_95006": "提取常数", "Extract_function_95005": "提取函数", "Extract_to_0_in_1_95004": "提取到 {1} 中的 {0}", "Extract_to_0_in_1_scope_95008": "提取到 {1} 范围中的 {0}", "Extract_to_0_in_enclosing_scope_95007": "提取到封闭范围中的 {0}", "Extract_to_interface_95090": "提取到接口", "Extract_to_type_alias_95078": "提取到类型别名", "Extract_to_typedef_95079": "提取到类型引用", "Extract_type_95077": "提取类型", "FILE_6035": "文件", "FILE_OR_DIRECTORY_6040": "文件或目录", "Failed_to_parse_file_0_Colon_1_5014": "未能分析文件“{0}”: {1}。", "Fallthrough_case_in_switch_7029": "switch 语句中的 Fallthrough 情况。", "File_0_does_not_exist_6096": "文件“{0}”不存在。", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "根据前面缓存的查找，文件“{0}”不存在。", "File_0_exist_use_it_as_a_name_resolution_result_6097": "文件“{0}”存在 - 将其用作名称解析结果。", "File_0_exists_according_to_earlier_cached_lookups_6239": "根据前面缓存的查找，文件“{0}”存在。", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "文件“{0}”具有不受支持的扩展名。仅支持 {1} 扩展名。", "File_0_has_an_unsupported_extension_so_skipping_it_6081": "文件“{0}”的扩展名不受支持，正在跳过。", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "文件 \"{0}\" 是 JavaScript 文件。你是想启用 \"allowJs\" 选项吗?", "File_0_is_not_a_module_2306": "文件“{0}”不是模块。", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "文件 \"{0}\" 不在项目 \"{1}\" 的文件列表中。项目必须列出所有文件，或使用 \"include\" 模式。", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "文件“{0}”不在 \"rootDir\"“{1}”下。\"rootDir\" 应包含所有源文件。", "File_0_not_found_6053": "找不到文件“{0}”。", "File_Management_6245": "文件管理", "File_change_detected_Starting_incremental_compilation_6032": "检测到文件更改。正在启动增量编译...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "文件是 CommonJS 模块，因为“{0}”没有字段 “type”", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "文件是 CommonJS 模块，因为“{0}”具有值不是 “module” 的字段 “type”", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "文件是 CommonJS 模块，因为找不到 “package.json”", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "文件是 ECMAScript 模块，因为“{0}”具有值为 “module” 的字段 “type”", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "文件是 CommonJS 模块; 它可能会转换为 ES 模块。", "File_is_default_library_for_target_specified_here_1426": "文件是此处指定的目标的默认库。", "File_is_entry_point_of_type_library_specified_here_1419": "文件是此处指定的类型库的入口点。", "File_is_included_via_import_here_1399": "在此处通过导入包含了文件。", "File_is_included_via_library_reference_here_1406": "在此处通过库引用包含了文件。", "File_is_included_via_reference_here_1401": "在此处通过引用包含了文件。", "File_is_included_via_type_library_reference_here_1404": "在此处通过类型库引用包含了文件。", "File_is_library_specified_here_1423": "文件是此处指定的库。", "File_is_matched_by_files_list_specified_here_1410": "通过此处指定的“文件”列表匹配了文件。", "File_is_matched_by_include_pattern_specified_here_1408": "通过在此处指定包含模式匹配了文件。", "File_is_output_from_referenced_project_specified_here_1413": "从此处指定的引用项目输出文件。", "File_is_output_of_project_reference_source_0_1428": "文件是项目引用源 \"{0}\" 的输出", "File_is_source_from_referenced_project_specified_here_1416": "文件源自此处指定的引用项目。", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "文件名“{0}”仅在大小写方面与包含的文件名“{1}”不同。", "File_name_0_has_a_1_extension_stripping_it_6132": "文件名“{0}”的扩展名为“{1}”，请去除它。", "File_redirects_to_file_0_1429": "文件重定向到文件 \"{0}\"", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "文件规范不能包含出现在递归目录通配符(\"*\"): “{0}”后的父目录(\"..\")。", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "文件规范不能以递归目录通配符结尾(\"**\"):“{0}”。", "Filters_results_from_the_include_option_6627": "从 “include” 选项筛选结果。", "Fix_all_detected_spelling_errors_95026": "修复检测到的所有拼写错误", "Fix_all_expressions_possibly_missing_await_95085": "修复可能缺少 \"await\" 的所有表达式", "Fix_all_implicit_this_errors_95107": "修复所有 implicit-'this' 错误", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "修复所有错误的异步函数返回类型", "For_await_loops_cannot_be_used_inside_a_class_static_block_18038": "无法在类静态块内使用 for...await 循环。", "Found_0_errors_6217": "找到 {0} 个错误。", "Found_0_errors_Watching_for_file_changes_6194": "找到 {0} 个错误。注意文件更改。", "Found_0_errors_in_1_files_6261": "在 {1} 个文件中找到 {0} 个错误。", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "在同一文件中找到 {0} 个错误，起始位置为: {1}", "Found_1_error_6216": "找到 1 个错误。", "Found_1_error_Watching_for_file_changes_6193": "找到 1 个错误。注意文件更改。", "Found_1_error_in_1_6259": "在 {1} 中找到 1 个错误", "Found_package_json_at_0_6099": "在“{0}”处找到了 \"package.json\"。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_1250": "面向 \"ES3\" 或 \"ES5\" 时，在严格模式下，块内不允许函数声明。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Class_d_1251": "面向 \"ES3\" 或 \"ES5\" 时，在严格模式下，块内不允许函数声明。类定义自动处于严格模式。", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES3_or_ES5_Modules_1252": "面向 \"ES3\" 或 \"ES5\" 时，在严格模式下，块内不允许函数声明。模块自动处于严格模式。", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "缺少返回类型批注的函数表达式隐式具有“{0}”返回类型。", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "函数实现缺失或未立即出现在声明之后。", "Function_implementation_name_must_be_0_2389": "函数实现名称必须为“{0}”。", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "由于函数不具有返回类型批注并且在它的一个返回表达式中得到直接或间接引用，因此它隐式具有返回类型 \"any\"。", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "函数缺少结束 return 语句，返回类型不包括 \"undefined\"。", "Function_not_implemented_95159": "未实现函数。", "Function_overload_must_be_static_2387": "函数重载必须为静态。", "Function_overload_must_not_be_static_2388": "函数重载不能为静态。", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "在联合类型中使用时，函数类型标记必须用括号括起来。", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "在相交类型中使用时，函数类型标记必须用括号括起来。", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "缺少返回类型注释的函数类型隐式具有 \"{0}\" 返回类型。", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "具有正文的函数只能与环境类合并。", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "从项目的 TypeScript 和 JavaScript 文件生成 .d.ts 文件。", "Generate_get_and_set_accessors_95046": "生成 \"get\" 和 \"set\" 访问器", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "为所有重写属性生成 \"get\" 和 \"set\" 访问器", "Generates_a_CPU_profile_6223": "生成 CPU 配置文件。", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "为每个相应的 \".d.ts\" 文件生成源映射。", "Generates_an_event_trace_and_a_list_of_types_6237": "生成事件跟踪和类型列表。", "Generates_corresponding_d_ts_file_6002": "生成相应的 \".d.ts\" 文件。", "Generates_corresponding_map_file_6043": "生成相应的 \".map\" 文件。", "Generator_implicitly_has_yield_type_0_because_it_does_not_yield_any_values_Consider_supplying_a_retu_7025": "生成器隐式具有产出类型 \"{0}\" ，因为它不生成任何值。请考虑提供一个返回类型注释。", "Generators_are_not_allowed_in_an_ambient_context_1221": "不允许在环境上下文中使用生成器。", "Generic_type_0_requires_1_type_argument_s_2314": "泛型类型“{0}”需要 {1} 个类型参数。", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "泛型类型“{0}”需要介于 {1} 和 {2} 类型参数之间。", "Global_module_exports_may_only_appear_at_top_level_1316": "全局模块导出仅可出现在顶层级别中。", "Global_module_exports_may_only_appear_in_declaration_files_1315": "全局模块导出仅可出现声明文件中。", "Global_module_exports_may_only_appear_in_module_files_1314": "全局模块导出仅可出现模块文件中。", "Global_type_0_must_be_a_class_or_interface_type_2316": "全局类型“{0}”必须为类或接口类型。", "Global_type_0_must_have_1_type_parameter_s_2317": "全局类型“{0}”必须具有 {1} 个类型参数。", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "在 \"--incremental\" 和 \"--watch\" 中有重新编译，假定文件中的更改只会影响直接依赖它的文件。", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "在使用 “incremental” 和 “watch” 模式的项目中具有重新编译会假定文件中的更改将仅直接影响依赖于它的文件。", "Hexadecimal_digit_expected_1125": "应为十六进制数字。", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "应为标识符。“{0}”是模块顶层的预留字。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "应为标识符。“{0}”在严格模式下是保留字。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "应为标识符。“{0}”在严格模式下是保留字。类定义自动处于严格模式。", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "应为标识符。“{0}”是严格模式下的保留字。模块自动处于严格模式。", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "应为标识符。\"{0}\" 是保留字，不能在此处使用。", "Identifier_expected_1003": "应为标识符。", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "应为标识符。转换 ECMAScript 模块时，\"__esModule\" 保留为导出标记。", "Identifier_or_string_literal_expected_1478": "应为标识符或字符串字面量。", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "如果“{0}”包实际上公开此模块，请考虑发送拉取请求以修正“https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}”", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "如果“{0}”包实际公开了此模块，请尝试添加包含 `declare module‘{1}';` 的新声明(.d.ts)文件", "Ignore_this_error_message_90019": "忽略此错误信息", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "忽略 tsconfig.json，使用默认编译器选项编译指定文件。", "Implement_all_inherited_abstract_classes_95040": "实现继承的所有抽象类", "Implement_all_unimplemented_interfaces_95032": "实现未实现的所有接口", "Implement_inherited_abstract_class_90007": "实现已继承的抽象类", "Implement_interface_0_90006": "实现接口“{0}”", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "导出的类“{0}”的 Implements 子句具有或正在使用专用名称“{1}”。", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "\"symbol\" 到 \"string\" 的隐式转换将在运行时失败。请考虑在 \"String(...)\" 中包装此表达式。", "Import_0_from_1_90013": "从“{1}”导入“{0}”", "Import_assertion_values_must_be_string_literal_expressions_2837": "导入断言值必须为字符串字面量表达式。", "Import_assertions_are_not_allowed_on_statements_that_transpile_to_commonjs_require_calls_2836": "不允许在转译到 commonjs “require” 调用的语句导入断言。", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_or_nodenext_2821": "仅在将 “--module” 选项设置为 “esnext” 或 “nodenext” 时，才支持导入断言。", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "导入断言不能用于仅类型导入或导出。", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "面向 ECMAScript 模块时，不能使用导入分配。请考虑改用 \"import * as ns from \"mod\"\"、\"import {a} from \"mod\"\"、\"import d from \"mod\"\" 或另一种模块格式。", "Import_declaration_0_is_using_private_name_1_4000": "导入声明“{0}”使用的是专用名称“{1}”。", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "导入声明与“{0}”的局部声明冲突。", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "命名空间中的导入声明不能引用模块。", "Import_emit_helpers_from_tslib_6139": "从 \"tslib\" 导入发出帮助程序。", "Import_may_be_converted_to_a_default_import_80003": "导入可能会转换为默认导入。", "Import_name_cannot_be_0_2438": "导入名称不能为“{0}”。", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "环境模块声明中的导入或导出声明不能通过相对模块名引用模块。", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "路径 '{1}' 处的 package.json 作用域中不存在导入说明符 '{0}'。", "Imported_via_0_from_file_1_1393": "通过 {0} 从文件 \"{1}\" 导入", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "通过 {0} 从文件 \"{1}\" 导入，以按照 compilerOptions 中指定的配置导入 \"importHelpers\"", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "通过 {0} 从文件 \"{1}\" 导入，以导入 \"jsx\" 和 \"jsxs\" 工厂函数", "Imported_via_0_from_file_1_with_packageId_2_1394": "通过 {0} 从具有 packageId \"{2}\" 的文件 \"{1}\" 导入", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "通过 {0} 从具有 packageId \"{2}\" 的文件 \"{1}\" 导入，以按照 compilerOptions 中指定的方式导入 \"importHelpers\"", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "通过 {0} 从具有 packageId \"{2}\" 的文件 \"{1}\" 导入，以导入 \"jsx\" 和 \"jsxs\" 工厂函数", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "模块扩大中不允许导入。请考虑将它们移动到封闭的外部模块。", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "在环境枚举声明中，成员初始化表达式必须是常数表达式。", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "在包含多个声明的枚举中，只有一个声明可以省略其第一个枚举元素的初始化表达式。", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "包含文件列表。这不支持 glob 模式，与 “include” 不同。", "Include_modules_imported_with_json_extension_6197": "包括通过 \".json\" 扩展导入的模块", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "在发出的 JavaScript 内的源映射中包含源代码。", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "在发出的 JavaScript 中包括源映射文件。", "Includes_imports_of_types_referenced_by_0_90054": "包含由“{0}”引用的类型的导入", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "包括 --watch，-w 将开始监视当前项目的文件更改。设置后，可以使用以下内容配置监视模式:", "Index_signature_for_type_0_is_missing_in_type_1_2329": "类型“{1}”中缺少类型“{0}”的索引签名。", "Index_signature_in_type_0_only_permits_reading_2542": "类型“{0}”中的索引签名仅允许读取。", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "合并声明“{0}”中的单独声明必须全为导出或全为局部声明。", "Infer_all_types_from_usage_95023": "从使用情况推导所有类型", "Infer_function_return_type_95148": "推断函数返回类型", "Infer_parameter_types_from_usage_95012": "根据使用情况推断参数类型", "Infer_this_type_of_0_from_usage_95080": "从用法中推断出 \"{0}\" 的 \"this\" 类型", "Infer_type_of_0_from_usage_95011": "根据使用情况推断“{0}”的类型", "Initialize_property_0_in_the_constructor_90020": "初始化构造函数中的属性“{0}”", "Initialize_static_property_0_90021": "初始化静态属性“{0}”", "Initializer_for_property_0_2811": "属性“{0}”的初始化表达式", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "实例成员变量“{0}”的初始化表达式不能引用构造函数中声明的标识符“{1}”。", "Initializer_provides_no_value_for_this_binding_element_and_the_binding_element_has_no_default_value_2525": "初始化表达式没有为此绑定元素提供此任何值，且该绑定元素没有默认值。", "Initializers_are_not_allowed_in_ambient_contexts_1039": "不允许在环境上下文中使用初始化表达式。", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "初始化 TypeScript 项目并创建 tsconfig.json 文件。", "Insert_command_line_options_and_files_from_a_file_6030": "从文件插入命令行选项和文件。", "Install_0_95014": "安装“{0}”", "Install_all_missing_types_packages_95033": "安装缺少的所有类型包", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "接口“{0}”不能同时扩展类型“{1}”和“{2}”。", "Interface_0_incorrectly_extends_interface_1_2430": "接口“{0}”错误扩展接口“{1}”。", "Interface_declaration_cannot_have_implements_clause_1176": "接口声明不能有 \"implements\" 子句。", "Interface_must_be_given_a_name_1438": "必须为接口指定名称。", "Interface_name_cannot_be_0_2427": "接口名称不能为“{0}”。", "Interop_Constraints_6252": "互操作约束", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "将可选属性类型解释为已写，而不是添加 \"undefined\"。", "Invalid_character_1127": "无效的字符。", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "无效的导入说明符 '{0}' 没有可行的解决方法。", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "扩大中的模块名称无效。模块“{0}”解析到位于“{1}”处的非类型化模块，其无法扩大。", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "扩大中的模块名无效，找不到模块“{0}”。", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "新表达式中的可选链无效。是否要调用“{0}()”?", "Invalid_reference_directive_syntax_1084": "\"reference\" 指令语法无效。", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "“{0}”的使用无效。它不能在类静态块内使用。", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "“{0}”的使用无效。模块自动处于严格模式。", "Invalid_use_of_0_in_strict_mode_1100": "严格模式下“{0}”的使用无效。", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "\"jsxFactory\" 的值无效。“{0}”不是有效的标识符或限定名称。", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "\"jsxFragmentFactory\" 的值无效。“{0}”不是有效的标识符或限定名称。", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "\"--reactNamespace\" 的值无效。“{0}”不是有效的标识符。", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "很可能缺少了分隔这两个模板表达式的逗号。它们构成了无法调用的带标记的模板表达式。", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "其元素类型 \"{0}\" 不是有效的 JSX 元素。", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "其实例类型 \"{0}\" 不是有效的 JSX 元素。", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "其返回类型 \"{0}\" 不是有效的 JSX 元素。", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "<PERSON><PERSON><PERSON> \"@{0} {1}\" 不匹配 \"extends {2}\" 子句。", "JSDoc_0_is_not_attached_to_a_class_8022": "JSDoc \"@{0}\" 未附加到类。", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "JSDoc \"...\" 可能仅出现在签名的最后一个参数中。", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "JSDoc \"@param\" 标记具有名称 \"{0}\"，但不存在具有该名称的参数。", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "JSDoc \"@param\" 标记的名称为“{0}”，但该名称没有参数。如果它为数组类型，将匹配 \"arguments\"。", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "JSDoc \"@typedef\" 标记应具有类型注释，或其后跟有 \"@property\" 或 \"@member\" 标记。", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "JSDoc 类型只能在文档注释内部使用。", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "JSDoc 类型可能会移到 TypeScript 类型。", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "只能为 JSX 属性分配非空“表达式”。", "JSX_element_0_has_no_corresponding_closing_tag_17008": "JSX 元素“{0}”没有相应的结束标记。", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "JSX 元素类不支持特性，因为它不具有“{0}”属性。", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "JSX 元素隐式具有类型 \"any\"，因为不存在接口 \"JSX.{0}\"。", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "JSX 元素隐式具有类型 \"any\"，因为不存在全局类型 \"JSX.Element\"。", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "JSX 元素类型“{0}”不具有任何构造签名或调用签名。", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "JSX 元素不能具有多个名称相同的特性。", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "JSX 表达式不能使用逗号运算符。你是想写入数组吗?", "JSX_expressions_must_have_one_parent_element_2657": "JSX 表达式必须具有一个父元素。", "JSX_fragment_has_no_corresponding_closing_tag_17014": "JSX 片段没有相应的结束标记。", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "JSX 属性访问表达式不能包含 JSX 命名空间名称", "JSX_spread_child_must_be_an_array_type_2609": "JSX 扩展子属性必须为数组类型。", "JavaScript_Support_6247": "JavaScript 支持", "Jump_target_cannot_cross_function_boundary_1107": "跳转目标不能跨越函数边界。", "KIND_6034": "种类", "Keywords_cannot_contain_escape_characters_1260": "关键字不能包含转义字符。", "LOCATION_6037": "位置", "Language_and_Environment_6254": "语言和环境", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "逗号运算符的左侧未使用，没有任何副作用。", "Library_0_specified_in_compilerOptions_1422": "CompilerOptions 中指定了库 \"{0}\"", "Library_referenced_via_0_from_file_1_1405": "通过 \"{0}\" 从文件 \"{1}\" 引用了库", "Line_break_not_permitted_here_1142": "不允许在此处换行。", "Line_terminator_not_permitted_before_arrow_1200": "箭头前不允许有行终止符。", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "解析模块时要搜索的文件名后缀列表。", "List_of_folders_to_include_type_definitions_from_6161": "包含类型定义来源的文件夹列表。", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "根文件夹列表，其组合内容表示在运行时的项目结构。", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "正在从根目录“{1}”加载“{0}”，候选位置“{2}”。", "Loading_module_0_from_node_modules_folder_target_file_type_1_6098": "正在从 \"node_modules\" 文件夹加载模块“{0}”，目标文件类型“{1}”。", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_type_1_6095": "正在将模块作为文件/文件夹进行加载，候选模块位置“{0}”，目标文件类型“{1}”。", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "区域设置必须采用 <语言> 或 <语言>-<区域> 形式。例如“{0}”或“{1}”。", "Log_paths_used_during_the_moduleResolution_process_6706": "在 “moduleResolution” 进程期间使用的日志路径。", "Longest_matching_prefix_for_0_is_1_6108": "“{0}”的最长匹配前缀为“{1}”。", "Looking_up_in_node_modules_folder_initial_location_0_6125": "正在 \"node_modules\" 文件夹中查找，初始位置为“{0}”。", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "让所有 \"super()\" 调用成为构造函数中的第一个语句", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "使 keyof 仅返回字符串，而不是字符串、数字或符号。旧版选项。", "Make_super_call_the_first_statement_in_the_constructor_90002": "在构造函数中，使 \"super()\" 调用第一个语句", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "映射的对象类型隐式地含有 \"any\" 模板类型。", "Matched_0_condition_1_6403": "匹配的“{0}”条件“{1}”。", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "默认情况下匹配包括模式 “**/*”", "Matched_by_include_pattern_0_in_1_1407": "通过在 \"{1}\" 中的包含模式 \"{0}\" 匹配", "Member_0_implicitly_has_an_1_type_7008": "成员“{0}”隐式包含类型“{1}”。", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "成员 \"{0}\" 隐式具有 \"{1}\" 类型，但可以从用法中推断出更好的类型。", "Merge_conflict_marker_encountered_1185": "遇到合并冲突标记。", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "合并声明“{0}”不能包含默认导出声明。请考虑改为添加一个独立的“导出默认 {0}”声明。", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "元属性“{0}”只能在函数声明、函数表达式或构造函数的主体中使用。", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "方法“{0}”不能具有实现，因为它标记为抽象。", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "导出接口的方法“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "导出接口的方法“{0}”具有或正在使用专用名称“{1}”。", "Method_not_implemented_95158": "方法未实现。", "Modifiers_cannot_appear_here_1184": "修饰符不能出现在此处。", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "模块 \"{0}\" 只能在使用 \"{1}\" 标志时进行默认导入", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "无法使用此构造导入模块“{0}”。说明符仅解析为 ES 模块，后者不能使用“require”进行导入。请改用 ECMAScript 导入。", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "模块 \"{0}\" 在本地声明 \"{1}\"，但它被导出为 \"{2}\"。", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "模块 \"{0}\" 在本地声明 \"{1}\"，但未导出它。", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "模块 \"{0}\" 不引用类型，但在此处用作类型。你是想使用 \"typeof import('{0}')\" 吗?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "模块“{0}”不引用值，但在此处用作值。", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "模块 {0} 已导出一个名为“{1}”的成员。请考虑重新显式导出以解决歧义。", "Module_0_has_no_default_export_1192": "模块“{0}”没有默认导出。", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "模块 \"{0}\" 没有默认导出。你是想改为使用 \"import { {1} } from {0}\" 吗?", "Module_0_has_no_exported_member_1_2305": "模块“{0}”没有导出的成员“{1}”。", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "模块 \"{0}\" 没有导出的成员 \"{1}\"。你是想改用 \"import {1} from {0}\" 吗?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "模块“{0}”被具有相同名称的局部声明隐藏。", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "模块“{0}”使用 \"export =\" 且无法与 \"export *\" 一起使用。", "Module_0_was_resolved_as_ambient_module_declared_in_1_since_this_file_was_not_modified_6145": "模块“{0}”解析为“{1}”中声明的环境模块，因为未修改此文件。", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "模块“{0}”解析为文件“{1}”中本地声明的环境模块。", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "模块“{0}”已解析为“{1}”，但尚未设置 \"--jsx\"。", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "模块 \"{0}\" 已解析为 \"{1}\"，但未使用 \"--resolveJsonModule\"。", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "模块声明名称只能使用 ' 或 \" 引用字符串。", "Module_name_0_matched_pattern_1_6092": "模块名“{0}”，匹配的模式“{1}”。", "Module_name_0_was_not_resolved_6090": "======== 未解析模块名“{0}”。========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== 模块名“{0}”已成功解析为“{1}”。========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== 模块名 \"{0}\" 已成功解析为 \"{1}\"，包 ID 为 \"{2}\"。========", "Module_resolution_kind_is_not_specified_using_0_6088": "未指定模块解析类型，正在使用“{0}”。", "Module_resolution_using_rootDirs_has_failed_6111": "使用 \"rootDirs\" 的模块解析失败。", "Modules_6244": "模块", "Move_labeled_tuple_element_modifiers_to_labels_95117": "将已标记的元组元素修饰符移至标签", "Move_to_a_new_file_95049": "移动到新的文件", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "不允许使用多个连续的数字分隔符。", "Multiple_constructor_implementations_are_not_allowed_2392": "不允许存在多个构造函数实现。", "NEWLINE_6061": "换行符", "Name_is_not_valid_95136": "名称无效", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "“{1}”和“{2}”类型的命名属性“{0}”不完全相同。", "Namespace_0_has_no_exported_member_1_2694": "命名空间“{0}”没有已导出的成员“{1}”。", "Namespace_must_be_given_a_name_1437": "必须为命名空间指定名称。", "Namespace_name_cannot_be_0_2819": "命名空间名称不能为“{0}”。", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "没有任何基构造函数具有指定数量的类型参数。", "No_constituent_of_type_0_is_callable_2755": "不可调用 \"{0}\" 类型的任何组成部分。", "No_constituent_of_type_0_is_constructable_2759": "不可构造 \"{0}\" 类型的任何组成部分。", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "在类型 \"{1}\" 上找不到具有类型为 \"{0}\" 的参数的索引签名。", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "在配置文件“{0}”中找不到任何输入。指定的 \"include\" 路径为“{1}”，\"exclude\" 路径为“{2}”。", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "不再受支持。在早期版本中，手动设置用于读取文件的文本编码。", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "没有需要 {0} 参数的重载，但存在需要 {1} 或 {2} 参数的重载。", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "没有需要 {0} 类型参数的重载，但存在需要 {1} 或 {2} 类型参数的重载。", "No_overload_matches_this_call_2769": "没有与此调用匹配的重载。", "No_type_could_be_extracted_from_this_type_node_95134": "无法从该类型节点中提取任何类型", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "速记属性 \"{0}\" 的范围内不存在任何值。请声明一个值或提供一个初始值设定项。", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "非抽象类“{0}”不会实现继承自“{2}”类的抽象成员“{1}”。", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "非抽象类表达式不会实现继承自“{1}”类的抽象成员“{0}”。", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "非 null 断言只能在 TypeScript 文件中使用。", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "未设置 \"baseUrl\" 时，不允许使用非相对路径。是否忘记了前导 \"./\"?", "Non_simple_parameter_declared_here_1348": "此处声明了非简单参数。", "Not_all_code_paths_return_a_value_7030": "并非所有代码路径都返回值。", "Not_all_constituents_of_type_0_are_callable_2756": "\"{0}\" 类型的部分要素不可调用。", "Not_all_constituents_of_type_0_are_constructable_2760": "\"{0}\" 类型的部分要素不可构造。", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "绝对值大于或等于 2^53 的数值文本过大，无法用整数准确表示。", "Numeric_separators_are_not_allowed_here_6188": "此处不允许使用数字分隔符。", "Object_is_of_type_unknown_2571": "对象的类型为 \"unknown\"。", "Object_is_possibly_null_2531": "对象可能为 \"null\"。", "Object_is_possibly_null_or_undefined_2533": "对象可能为 \"null\" 或“未定义”。", "Object_is_possibly_undefined_2532": "对象可能为“未定义”。", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "对象字面量只能指定已知属性，并且“{0}”不在类型“{1}”中。", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "对象字面量只能指定已知的属性，但“{0}”中不存在类型“{1}”。是否要写入 {2}?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "对象字面量的属性“{0}”隐式含有“{1}”类型。", "Octal_digit_expected_1178": "需要八进制数字。", "Octal_literal_types_must_use_ES2015_syntax_Use_the_syntax_0_8017": "八进制文本类型必须使用 ES2015 语法。请使用语法“{0}”。", "Octal_literals_are_not_allowed_in_enums_members_initializer_Use_the_syntax_0_8018": "枚举成员初始值设定项中不允许有八进制文本。请使用语法“{0}”。", "Octal_literals_are_not_allowed_in_strict_mode_1121": "严格模式下不允许使用八进制文本。", "Octal_literals_are_not_available_when_targeting_ECMAScript_5_and_higher_Use_the_syntax_0_1085": "当面向 ECMAScript 5 及更高版本时，不能使用八进制文本。请使用语法“{0}”。", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "\"for...in\" 语句中只允许单个变量声明。", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "\"for...of\" 语句中只允许单个变量声明。", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "使用 \"new\" 关键字只能调用 void 函数。", "Only_ambient_modules_can_use_quoted_names_1035": "仅环境模块可使用带引号的名称。", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "--{0} 旁仅支持 \"amd\" 和 \"system\" 模块。", "Only_emit_d_ts_declaration_files_6014": "仅发出 \".d.ts\" 声明文件。 ", "Only_named_exports_may_use_export_type_1383": "只有已命名的导出可使用“导出类型”。", "Only_numeric_enums_can_have_computed_members_but_this_expression_has_type_0_If_you_do_not_need_exhau_18033": "只有数字枚举可具有计算成员，但此表达式的类型为“{0}”。如果不需要全面性检查，请考虑改用对象文本。", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "仅输出 d.ts 文件，而不输出 JavaScript 文件。", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "通过 \"super\" 关键字只能访问基类的公共方法和受保护方法。", "Operator_0_cannot_be_applied_to_type_1_2736": "运算符 \"{0}\" 不能应用于类型 \"{1}\"。", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "运算符“{0}”不能应用于类型“{1}”和“{2}”。", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "在编辑时选择项目退出多项目引用检查。", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "选项“{0}”只能在 \"tsconfig.json\" 文件中指定，或者在命令行上设置为 \"false\" 或 \"null\"。", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "选项“{0}”只能在 \"tsconfig.json\" 文件中指定或在命令行上设置为 \"null\"。", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "仅当提供了选项 \"--inlineSourceMap\" 或选项 \"--sourceMap\" 时，才能使用选项“{0}”。", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "选项 \"jsx\" 为“{1}”时，不能指定选项“{0}”。", "Option_0_cannot_be_specified_when_option_target_is_ES3_5048": "选项 \"target\" 为 \"ES3\" 时，不能指定选项 \"{0}\"。", "Option_0_cannot_be_specified_with_option_1_5053": "选项“{0}”不能与选项“{1}”同时指定。", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "无法在不指定选项“{1}”的情况下指定选项“{0}”。", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "无法在不指定选项 {1} 或选项 {2} 的情况下指定选项 {0}。", "Option_build_must_be_the_first_command_line_argument_6369": "选项 '--build' 必须是第一个命令行参数。", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "选项 “--incremental” 只能使用 tsconfig 指定，在发出到单个文件时指定，或在指定了选项 “--tsBuildInfoFile” 时指定。", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "选项 \"isolatedModules\" 只可在提供了选项 \"--module\" 或者选项 \"target\" 是 \"ES2015\" 或更高版本时使用。", "Option_preserveConstEnums_cannot_be_disabled_when_isolatedModules_is_enabled_5091": "启用 \"isolatedModules\" 时，无法禁用选项 \"preserveConstEnums\"。", "Option_preserveValueImports_can_only_be_used_when_module_is_set_to_es2015_or_later_5095": "选项 \"preserveValueImports\" 只能在 \"module\" 设置为 \"es2015\" 或更高版本时使用。", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "选项 \"project\" 在命令行上不能与源文件混合使用。", "Option_resolveJsonModule_can_only_be_specified_when_module_code_generation_is_commonjs_amd_es2015_or_5071": "仅当模块代码生成为 \"commonjs\"、\"amd\"、\"es2015\" 或 \"esNext\" 时，才能指定选项 \"--resolveJsonModule\"。", "Option_resolveJsonModule_cannot_be_specified_without_node_module_resolution_strategy_5070": "在没有 \"node\" 模块解析策略的情况下，无法指定选项 \"-resolveJsonModule\"。", "Options_0_and_1_cannot_be_combined_6370": "选项“{0}”与“{1}”不能组合在一起。", "Options_Colon_6027": "选项:", "Output_Formatting_6256": "输出格式设置", "Output_compiler_performance_information_after_building_6615": "生成后输出编译器性能信息。", "Output_directory_for_generated_declaration_files_6166": "已生成声明文件的输出目录。", "Output_file_0_from_project_1_does_not_exist_6309": "来自项目“{1}”的输出文件“{0}”不存在", "Output_file_0_has_not_been_built_from_source_file_1_6305": "未从源文件“{1}”生成输出文件“{0}”。", "Output_from_referenced_project_0_included_because_1_specified_1411": "由于指定了 \"{1}\"，因此包含了引用的项目 \"{0}\" 的输出", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "由于已将 \"--module\" 指定为 \"none\"，因此包含了引用的项目 \"{0}\" 的输出", "Output_more_detailed_compiler_performance_information_after_building_6632": "生成后输出更详细的编译器性能信息。", "Overload_0_of_1_2_gave_the_following_error_2772": "第 {0} 个重载(共 {1} 个)，“{2}”，出现以下错误。", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "重载签名必须都是抽象的或都是非抽象的。", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "重载签名必须全部为环境签名或非环境签名。", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "重载签名必须均导出或均不导出。", "Overload_signatures_must_all_be_optional_or_required_2386": "重载签名必须全部为可选签名或必需签名。", "Overload_signatures_must_all_be_public_private_or_protected_2385": "重载签名必须全部是公共签名、私有签名或受保护签名。", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "参数“{0}”不能引用在它之后声明的标识符“{1}”。", "Parameter_0_cannot_reference_itself_2372": "参数“{0}”不能引用它自身。", "Parameter_0_implicitly_has_an_1_type_7006": "参数“{0}”隐式具有“{1}”类型。", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "参数 \"{0}\" 隐式具有 \"{1}\" 类型，但可以从用法中推断出更好的类型。", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "参数“{0}”和参数“{1}”的位置不一样。", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "访问器的参数 \"{0}\" 具有或正在使用外部模块 \"{2}\" 中的名称 \"{1}\" ，但不能为其命名。", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "访问器的参数 \"{0}\" 具有或正在使用专用模块 \"{2}\" 中的名称 \"{1}\" 。", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "访问器的参数 \"{0}\" 具有或正在使用专用名称 \"{1}\"。", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "导出接口中的调用签名的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "导出接口中的调用签名的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "导出类中的构造函数的参数“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "导出类中的构造函数的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "导出类中的构造函数的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "导出接口中的构造函数签名的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "导出接口中的构造函数签名的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "导出函数的参数“{0}”具有或正在使用外部模块 {2} 中的名称“{1}”，但不能为其命名。", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "导出函数的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "导出函数的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "来自导出接口的索引签名的参数“{0}”具有或正在使用来自私有模块“{2}”的名称“{1}”。", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "来自导出接口的索引签名的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "导出接口中的方法的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "导出接口中的方法的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "导出类中的公共方法的参数“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "导出类中的公共方法的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "导出类中的公共方法的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "导出类中的公共静态方法的参数“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "导出类中的公共静态方法的参数“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "导出类中的公共静态方法的参数“{0}”具有或正在使用专用名称“{1}”。", "Parameter_cannot_have_question_mark_and_initializer_1015": "参数不能包含问号和初始化表达式。", "Parameter_declaration_expected_1138": "应为参数声明。", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "参数具有名称，但不具有类型。你是想使用 \"{0}: {1}\" 吗?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "参数修饰符只能在 TypeScript 文件中使用。", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "导出类中的公共 setter“{0}”的参数类型具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "导出类中的公共 setter“{0}”的参数类型具有或正在使用专用名称“{1}”。", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "导出类中的公共静态 setter“{0}”的参数类型具有或正在使用私有模块“{2}”中的名称“{1}”。", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "导出类中的公共静态 setter“{0}”的参数类型具有或正在使用专用名称“{1}”。", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "以严格模式进行分析，并为每个源文件发出 \"use strict\" 指令。", "Part_of_files_list_in_tsconfig_json_1409": "tsconfig.js 中 \"files\" 列表的一部分", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "模式“{0}”最多只可具有一个 \"*\" 字符。", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "\"--diagnostics\" 或 \"--extendedDiagnostics\" 的性能计时在此会话中不可用。未能找到 Web 性能 API 的本机实现。", "Platform_specific_6912": "平台特定", "Prefix_0_with_an_underscore_90025": "带下划线的前缀“{0}”", "Prefix_all_incorrect_property_declarations_with_declare_95095": "使用 \"declare\" 作为所有错误的属性声明的前缀", "Prefix_all_unused_declarations_with_where_possible_95025": "尽可能在所有未使用的声明前添加前缀 \"_\"", "Prefix_with_declare_95094": "使用 \"declare\" 前缀", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "保留 JavaScript 输出中未使用的导入值，否则将删除这些值。", "Print_all_of_the_files_read_during_the_compilation_6653": "打印在编译过程中读取的所有文件。", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "打印在编译过程中读取的文件，包括包含它的原因。", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "打印文件的名称及编译包含这些文件的原因。", "Print_names_of_files_part_of_the_compilation_6155": "属于编译一部分的文件的打印名称。", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "打印编译包含的文件的名称，然后停止处理。", "Print_names_of_generated_files_part_of_the_compilation_6154": "属于编译一部分的已生成文件的打印名称。", "Print_the_compiler_s_version_6019": "打印编译器的版本。", "Print_the_final_configuration_instead_of_building_1350": "打印最终配置而不是生成。", "Print_the_names_of_emitted_files_after_a_compilation_6652": "编译后打印已发出文件的名称。", "Print_this_message_6017": "打印此消息。", "Private_accessor_was_defined_without_a_getter_2806": "定义了专用访问器，但没有 Getter。", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "不允许在变量声明中使用专用标识符。", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "不允许在类主体之外使用专用标识符。", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "专用标识符仅允许在类主体中使用，并且只能用作类成员声明的一部分、属性访问或用在 \"in\" 表达式的左侧", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "专用标识符仅在面向 ECMAScript 2015 和更高版本时可用。", "Private_identifiers_cannot_be_used_as_parameters_18009": "不能将专用标识符用作参数。", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "不能在类型参数上访问专用或受保护的成员 \"{0}\"。", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "无法生成项目“{0}”，因为其依赖项“{1}”有错误", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "无法生成项目 \"{0}\" ，因为未生成其依赖项 \"{1}\"", "Project_0_is_being_forcibly_rebuilt_6388": "正在强制重新生成项目“{0}”", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "项目“{0}”已过期，因为 buildinfo 文件“{1}”指示某些更改未发出", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "项目“{0}”已过期，因为其依赖项“{1}”已过期", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "项目“{0}”已过期，因为输出“{1}”早于输入“{2}”", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "项目“{0}”已过期，因为输出文件“{1}”不存在", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "项目 \"{0}\" 已过期，因为其输出是使用与当前版本 \"{2}\" 不同的版本 \"{1}\" 生成的", "Project_0_is_out_of_date_because_output_of_its_dependency_1_has_changed_6372": "项目 \"{0}\" 已过期，因为其依赖项 \"{1}\" 的输出已更改", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "项目“{0}”已过期，因为读取文件“{1}”时出错", "Project_0_is_up_to_date_6361": "“{0}”项目已是最新", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "项目“{0}”是最新的，因为最新的输入“{1}”早于输出“{2}”", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "项目“{0}”是最新的，但需要更新早于输入文件的输出文件的时间戳", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "项目“{0}”已是最新，拥有来自其依赖项的 .d.ts 文件", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "项目引用不能形成环形图。检测到循环: {0}", "Projects_6255": "项目", "Projects_in_this_build_Colon_0_6355": "此生成中的项目: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "只有在面向 ECMAScript 2015 及更高版本时，才可使用带有 \"accessor\" 修饰符的属性。", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "属性“{0}”不能具有初始化表杰式，因为它标记为摘要。", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "属性“{0}”来自索引签名，因此必须使用[“{0}”]访问它。", "Property_0_does_not_exist_on_type_1_2339": "类型“{1}”上不存在属性“{0}”。", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "属性“{0}”在类型“{1}”上不存在。你是否指的是“{2}”?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "属性“{0}”在类型“{1}”上不存在。你的意思是改为访问静态成员“{2}”吗?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "属性“{0}”在类型“{1}”上不存在。是否需要更改目标库? 请尝试将 “lib” 编译器选项更改为“{2}”或更高版本。", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "属性“{0}”在类型 “{1}” 上不存在。请尝试将 “lib” 编译器选项更改为包含 “dom”。", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "属性“{0}”没有初始化表达式，并且未在类静态块中明确分配。", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "属性“{0}”没有初始化表达式，且未在构造函数中明确赋值。", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "属性“{0}”隐式具有类型 \"any\"，因为其 get 访问器缺少返回类型批注。", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "属性“{0}”隐式具有类型 \"any\"，因为其 set 访问器缺少参数类型批注。", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "属性 \"{0}\" 隐式具有类型 \"any\"，但可从用法为其 get 访问器推断出更好类型。", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "属性 \"{0}\" 隐式具有类型 \"any\"，但可从用法为其 set 访问器推断出更好的类型。", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "类型“{1}”中的属性“{0}”不可分配给基类型“{2}”中的同一属性。", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "类型“{1}”中的属性“{0}”不可分配给类型“{2}”。", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "类型 \"{1}\" 中的属性 \"{0}\" 引用了不能从类型 \"{2}\" 内访问的其他成员。", "Property_0_is_declared_but_its_value_is_never_read_6138": "已声明属性“{0}”，但从未读取其值。", "Property_0_is_incompatible_with_index_signature_2530": "属性“{0}”与索引签名不兼容。", "Property_0_is_missing_in_type_1_2324": "类型“{1}”中缺少属性“{0}”。", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "类型 \"{1}\" 中缺少属性 \"{0}\"，但类型 \"{2}\" 中需要该属性。", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "属性 \"{0}\" 在类 \"{1}\" 外部不可访问，因为它具有专用标识符。", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "属性“{0}”在类型“{1}”中为可选，但在类型“{2}”中为必选。", "Property_0_is_private_and_only_accessible_within_class_1_2341": "属性“{0}”为私有属性，只能在类“{1}”中访问。", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "属性“{0}”在类型“{1}”中是私有属性，但在类型“{2}”中不是。", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "属性“{0}”受保护，只能通过类“{1}”的实例进行访问。这是类“{2}”的实例。", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "属性“{0}”受保护，只能在类“{1}”及其子类中访问。", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "属性“{0}”受保护，但类型“{1}”并不是从“{2}”派生的类。", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "属性“{0}”在类型“{1}”中受保护，但在类型“{2}”中为公共属性。", "Property_0_is_used_before_being_assigned_2565": "在赋值前使用了属性“{0}”。", "Property_0_is_used_before_its_initialization_2729": "属性 \"{0}\" 在其初始化前已被使用。", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "类型“{1}”上不存在属性“{0}”。你是否是指“{2}”?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "JSX 展开特性的“{0}”属性不能分配给目标属性。", "Property_0_of_exported_class_expression_may_not_be_private_or_protected_4094": "导出类表达式的属性“{0}”可能不是私密或受保护的属性。", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "导出接口的属性“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "导出接口的属性“{0}”具有或正在使用专用名称“{1}”。", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "类型“{1}”的属性“{0}”不能赋给“{2}”索引类型“{3}”。", "Property_0_was_also_declared_here_2733": "属性 \"{0}\" 也在此处声明。", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "属性 \"{0}\" 将覆盖 \"{1}\" 中的基属性。如果是有意的，请添加初始值设定项。否则，请添加 \"declare\" 修饰符或删除多余的声明。", "Property_assignment_expected_1136": "应为属性分配。", "Property_destructuring_pattern_expected_1180": "应为属性析构模式。", "Property_or_signature_expected_1131": "应为属性或签名。", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "属性值只能是字符串、数字、\"true\"、\"false\"、\"null\"、对象或数组等类型的字面量。", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_or_ES3_6179": "以 \"ES5\" 或 \"ES3\" 设为目标时，对 \"for-of\"、传播和析构中的可迭代项提供完全支持。", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "导出类的公共方法“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "导出类的公共方法“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "导出类的公共方法“{0}”具有或正在使用专用名称“{1}”。", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "导出类的公共属性“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "导出类的公共属性“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "导出类的公共属性“{0}”具有或正在使用专用名称“{1}”。", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "导出类的公共静态方法“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "导出类的公共静态方法“{0}”具有或正在使用私有模块“{2}”中的名称“{1}”。", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "导出类的公共静态方法“{0}”具有或正在使用专用名称“{1}”。", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "导出类的公共静态属性“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "导出类的公共静态属性“{0}”具有或正在使用外部模块“{2}”中的名称“{1}”。", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "导出类的公共静态属性“{0}”具有或正在使用专用名称“{1}”。", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "不允许使用限定名 \"{0}\"，因为没有前导 \"@param {object} {1}\"。", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "在未读取函数参数时引发错误。", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "对具有隐式 \"any\" 类型的表达式和声明引发错误。", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "在带隐式“any\" 类型的 \"this\" 表达式上引发错误。", "Re_exporting_a_type_when_the_isolatedModules_flag_is_provided_requires_using_export_type_1205": "提供 \"--isolatedModules\" 标志时，需要使用 \"export type\" 才能重新导出类型。", "Redirect_output_structure_to_the_directory_6006": "将输出结构重定向到目录。", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "减少 TypeScript 自动加载的项目数。", "Referenced_project_0_may_not_disable_emit_6310": "引用的项目“{0}”可能不会禁用发出。", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "引用的项目“{0}”必须拥有设置 \"composite\": true。", "Referenced_via_0_from_file_1_1400": "通过 \"{0}\" 从文件 \"{1}\" 引用", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2834": "当 “--moduleResolution” 为 “node16” 或 “nodenext” 时，相对导入路径需要 EcmaScript 导入中的显式文件扩展名。请考虑将扩展名添加到导入路径中。", "Relative_import_paths_need_explicit_file_extensions_in_EcmaScript_imports_when_moduleResolution_is_n_2835": "当 “--moduleResolution” 为 “node16” 或 “nodenext” 时，相对导入路径需要 EcmaScript 导入中的显式文件扩展名。你是否指的是“{0}”?", "Remove_a_list_of_directories_from_the_watch_process_6628": "从监视进程中删除目录列表。", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "从监视模式的处理中删除文件列表。", "Remove_all_unnecessary_override_modifiers_95163": "删除所有不必要的 \"override\" 修饰符", "Remove_all_unnecessary_uses_of_await_95087": "删除 \"await\" 的所有不必要的使用", "Remove_all_unreachable_code_95051": "删除所有无法访问的代码", "Remove_all_unused_labels_95054": "删除所有未使用的标签", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "从所有带有相关问题的箭头函数主体中删除大括号", "Remove_braces_from_arrow_function_95060": "从箭头函数中删除大括号", "Remove_braces_from_arrow_function_body_95112": "从箭头函数主体中删除大括号", "Remove_import_from_0_90005": "从“{0}”删除导入", "Remove_override_modifier_95161": "删除 \"override\" 修饰符", "Remove_parentheses_95126": "删除括号", "Remove_template_tag_90011": "删除模板标记", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "删除 TypeScript 语言服务器中 JavaScript 文件总源代码大小 20mb 的上限。", "Remove_type_from_import_declaration_from_0_90055": "从“{0}”中删除导入声明中的“type”", "Remove_type_from_import_of_0_from_1_90056": "从“{1}”中删除“{0}”导入中的“type”", "Remove_type_parameters_90012": "删除类型参数", "Remove_unnecessary_await_95086": "删除不必要的 \"await\"", "Remove_unreachable_code_95050": "删除无法访问的代码", "Remove_unused_declaration_for_Colon_0_90004": "为 \"{0}\" 删除未使用的声明", "Remove_unused_declarations_for_Colon_0_90041": "为“{0}”删除未使用的声明", "Remove_unused_destructuring_declaration_90039": "删除未使用的解构声明", "Remove_unused_label_95053": "删除未使用的标签", "Remove_variable_statement_90010": "删除变量语句", "Rename_param_tag_name_0_to_1_95173": "将 “@param” 标记名称“{0}”重命名为“{1}”", "Replace_0_with_Promise_1_90036": "将 \"{0}\" 替换为 \"Promise<{1}>\"", "Replace_all_unused_infer_with_unknown_90031": "将所有未使用的 \"infer\" 替换为 \"unknown\"", "Replace_import_with_0_95015": "用“{0}”替换导入。", "Replace_infer_0_with_unknown_90030": "将 \"infer {0}\" 替换为 \"unknown\"", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "在函数中的所有代码路径并非都返回值时报告错误。", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "报告 switch 语句中遇到 fallthrough 情况的错误。", "Report_errors_in_js_files_8019": ".js 文件中的报表出错。", "Report_errors_on_unused_locals_6134": "报告未使用的局部变量上的错误。", "Report_errors_on_unused_parameters_6135": "报告未使用的参数上的错误。", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "要求索引签名中有未声明的属性以使用元素访问。", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "所需的类型参数可能不遵循可选类型参数。", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "在位置“{1}”的缓存中找到模块“{0}”的解析。", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "在位置“{1}”的缓存中找到类型引用指令“{0}”的解析。", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "只将 \"keyof\" 解析为字符串值的属性名称(不含数字或符号)。", "Resolving_in_0_mode_with_conditions_1_6402": "正在 {0} 模式下解析，条件为 {1}。", "Resolving_module_0_from_1_6086": "======== 正在从“{1}”解析模块“{0}”。========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "正在相对于基 URL“{1}”-“{2}”解析模块名“{0}”。", "Resolving_real_path_for_0_result_1_6130": "正在解析“{0}”的真实路径，结果为“{1}”。", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== 正在解析类型引用指令“{0}”，包含文件“{1}”。========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== 正在解析类型引用指令“{0}”，包含文件“{1}”，根目录“{2}”。========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== 正在解析类型引用指令“{0}”，包含文件“{1}”，未设置根目录。========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== 正在解析类型引用指令“{0}”，未设置包含文件，根目录“{1}”。========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== 正在解析类型引用指令“{0}”，未设置包含文件，未设置根目录。========", "Resolving_with_primary_search_path_0_6121": "正在使用主搜索路径“{0}”解析。", "Rest_parameter_0_implicitly_has_an_any_type_7019": "Rest 参数“{0}”隐式具有 \"any[]\" 类型。", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "Rest 参数 \"{0}\" 隐式具有 \"any[]\" 类型，但可从用法中推断出更好的类型。", "Rest_types_may_only_be_created_from_object_types_2700": "rest 类型只能从对象类型创建。", "Return_type_annotation_circularly_references_itself_2577": "返回类型注释循环引用自身。", "Return_type_must_be_inferred_from_a_function_95149": "必须从函数中推断返回类型", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "导出接口中的调用签名的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "导出接口中的调用签名的返回类型具有或正在使用专用名称“{0}”。", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "导出接口中的构造函数签名的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "导出接口中的构造函数签名的返回类型具有或正在使用专用名称“{0}”。", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "构造函数签名的返回类型必须可赋给类的实例类型。", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "导出函数的返回类型具有或正在使用外部模块“{1}”中的名称“{0}”，但不能为其命名。", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "导出函数的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "导出函数的返回类型具有或正在使用专用名称“{0}”。", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "导出接口中的索引签名的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "导出接口中的索引签名的返回类型具有或正在使用专用名称“{0}”。", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "导出接口中的方法的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "导出接口中的方法的返回类型具有或正在使用专用名称“{0}”。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "导出类中的公共 getter“{0}”的返回类型具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "导出类中的公共 getter“{0}”的返回类型具有或正在使用私有模块“{2}”中的名称“{1}”。", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "导出类中的公共 getter“{0}”的返回类型具有或正在使用专用名称“{1}”。", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "导出类中的公共方法的返回类型具有或正在使用外部模块“{1}”中的名称“{0}”，但不能为其命名。", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "导出类中的公共方法的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "导出类中的公共方法的返回类型具有或正在使用专用名称“{0}”。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "导出类中的公共静态 getter“{0}”的返回类型具有或正在使用外部模块“{2}”中的名称“{1}”，但不能为其命名。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "导出类中的公共静态 getter“{0}”的返回类型具有或正在使用私有模块“{2}”中的名称“{1}”。", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "导出类中的公共静态 getter“{0}”的返回类型具有或正在使用专用名称“{1}”。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "导出类中的公共静态方法的返回类型具有或正在使用外部模块“{1}”中的名称“{0}”，但不能为其命名。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "导出类中的公共静态方法的返回类型具有或正在使用私有模块“{1}”中的名称“{0}”。", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "导出类中的公共静态方法的返回类型具有或正在使用专用名称“{0}”。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "正在重用从位置“{2}”缓存中找到的“{1}”中模块“{0}”的解析，但其未解析。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "正在重用从位置“{2}”缓存中找到的“{1}”中模块“{0}”的解析，已成功将其解析为“{3}”。", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "正在重用从位置“{2}”缓存中找到的“{1}”中模块“{0}”的解析，已成功将其解析为包 ID 为“{4}”的“{3}”。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "正在重用旧程序“{1}”中模块“{0}”的解析，但其未解析。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "正在重用旧程序“{1}”中模块“{0}”的解析，已成功将其解析为“{2}”。", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "正在重用旧程序“{1}”中模块“{0}”的解析，已成功将其解析为包 ID 为“{3}”的“{2}”。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "正在重用从位置“{2}”缓存中找到的“{1}”中类型引用指令“{0}”的解析，但其未解析。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "正在重用从位置“{2}”缓存中找到的“{1}”中类型引用指令“{0}”的解析，已成功将其解析为“{3}”。", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "正在重用从位置“{2}”缓存中找到的“{1}”中类型引用指令“{0}”的解析，已成功将其解析为包 ID 为“{4}”的“{3}”。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "正在重用旧程序“{1}”中类型引用指令“{0}”的解析，但其未解析。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "正在重用旧程序“{1}”中类型引用指令“{0}”的解析，已成功将其解析为“{2}”。", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "正在重用旧程序“{1}”中类型引用指令“{0}”的解析，已成功将其解析为包 ID 为“{3}”的“{2}”。", "Rewrite_all_as_indexed_access_types_95034": "全部重写为索引访问类型", "Rewrite_as_the_indexed_access_type_0_90026": "重写为索引访问类型“{0}”", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "无法确定根目录，正在跳过主搜索路径。", "Root_file_specified_for_compilation_1427": "为编译指定的根文件", "STRATEGY_6039": "策略", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "保存 .tsbuildinfo 文件以允许项目增量编译。", "Saw_non_matching_condition_0_6405": "看到了不匹配的条件“{0}”。", "Scoped_package_detected_looking_in_0_6182": "检测到范围包，请在“{0}”中查看", "Selection_is_not_a_valid_statement_or_statements_95155": "所选内容不是有效的语句", "Selection_is_not_a_valid_type_node_95133": "所选内容不是有效的类型节点", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "为发出的 JavaScript 设置 JavaScript 语言版本并包含兼容的库声明。", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "设置来自 TypeScript 的消息传递的语言。这不会影响发出。", "Set_the_module_option_in_your_configuration_file_to_0_95099": "将配置文件中的 \"module\" 选项设置为 \"{0}\"", "Set_the_newline_character_for_emitting_files_6659": "设置发出文件的换行符。", "Set_the_target_option_in_your_configuration_file_to_0_95098": "将配置文件中的 \"target\" 选项设置为 \"{0}\"", "Setters_cannot_return_a_value_2408": "Setter 不能返回值。", "Show_all_compiler_options_6169": "显示所有编译器选项。", "Show_diagnostic_information_6149": "显示诊断信息。", "Show_verbose_diagnostic_information_6150": "显示详细的诊断信息。", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "显示将生成(如果指定有 '--clean'，则删除)什么", "Signature_0_must_be_a_type_predicate_1224": "签名“{0}”必须为类型谓词。", "Skip_type_checking_all_d_ts_files_6693": "跳过对所有 .d.ts 文件的类型检查。", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "跳过 TypeScript 附带的类型检查 .d.ts 文件。", "Skip_type_checking_of_declaration_files_6012": "跳过声明文件的类型检查。", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "正在跳过项目“{0}”的生成，因为其依赖项“{1}”有错误", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "即将跳过项目 \"{0}\" 的生成，因为未生成其依赖项 \"{1}\"", "Source_from_referenced_project_0_included_because_1_specified_1414": "由于指定了 \"{1}\"，因此包含了引用的项目 \"{0}\" 的源", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "由于已将 \"--module\" 指定为 \"none\"，因此包含了引用的项目 \"{0}\" 的源", "Source_has_0_element_s_but_target_allows_only_1_2619": "源具有 {0} 个元素，但目标仅允许 {1} 个。", "Source_has_0_element_s_but_target_requires_1_2618": "源具有 {0} 个元素，但目标需要 {1} 个。", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "源不提供目标中位置 {0} 处所需元素的匹配项。", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "源不提供目标中位置 {0} 处可变元素的匹配项。", "Specify_ECMAScript_target_version_6015": "指定 ECMAScript 目标版本。", "Specify_JSX_code_generation_6080": "指定 JSX 代码生成。", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "指定将所有输出捆绑到一个 JavaScript 文件中的文件。如果 “declaration” 为 true，还要指定一个捆绑所有 .d.ts 输出的文件。", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "指定与要包含在编译中的文件匹配的 glob 模式列表。", "Specify_a_list_of_language_service_plugins_to_include_6681": "指定要包括的语言服务插件列表。", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "指定一组描述目标运行时环境的捆绑库声明文件。", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "指定一组将导入重新映射到其他查找位置的条目。", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "指定为项目指定路径的对象数组。在项目引用中使用。", "Specify_an_output_folder_for_all_emitted_files_6678": "为所有已发出的文件指定输出文件夹。", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "指定仅用于类型的导入的发出/检查行为。", "Specify_file_to_store_incremental_compilation_information_6380": "指定用于存储增量编译信息的文件", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "指定 TypeScript 如何从给定的模块说明符查找文件。", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "指定在缺少递归文件监视功能的系统上监视目录的方式。", "Specify_how_the_TypeScript_watch_mode_works_6715": "指定 TypeScript 监视模式的工作方式。", "Specify_library_files_to_be_included_in_the_compilation_6079": "指定要在编译中包括的库文件。", "Specify_module_code_generation_6016": "指定模块代码生成。", "Specify_module_resolution_strategy_Colon_node_Node_js_or_classic_TypeScript_pre_1_6_6069": "指定模块解析策略: \"node\" (Node.js)或 \"classic\" (TypeScript pre-1.6)。", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "指定使用 “jsx: react-jsx*” 时用于导入 JSX 中心函数的模块说明符。", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "指定多个行为类似于 “./node_modules/@types” 的文件夹。", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "指定对从中继承设置的基本配置文件的一个或多个路径或节点模块引用。", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "指定用于自动获取声明文件的选项。", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "指定在使用文件系统事件创建轮询监视失败时创建轮询监视的策略: \"FixedInterval\" (默认)、\"PriorityInterval\"、\"DynamicPriority\"、\"FixedChunkSize\"。", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "指定在不支持本机递归监视的平台上监视目录的策略: \"UseFsEvents\" (默认)、\"FixedPollingInterval\"、\"DynamicPriorityPolling\"、\"FixedChunkSizePolling\"。", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "指定监视文件的策略: \"FixedPollingInterval\" (默认)、\"PriorityPollingInterval\"、\"DynamicPriorityPolling\"、\"FixedChunkSizePolling\"、\"UseFsEvents\"、\"UseFsEventsOnParentDirectory\"。", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "指定在将 React JSX 发出设定为目标时用于片段的 JSX 片段引用，例如 “React.Fragment” 或 “Fragment”。", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "指定在设定 \"react\" JSX 发出目标时要使用的 JSX 工厂函数，例如 \"react.createElement\" 或 \"h\"。", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "指定在将 React JSX 发出设定为目标时要使用的 JSX 中心函数，例如 “react.createElement” 或 “h”。", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "当指定使用 \"jsxFactory\" 编译器选项面向 \"react\" JSX 发出时，指定要使用的 JSX 片段工厂函数，例如 \"Fragment\"。", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "指定基目录以解析非相关模块名称。", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "指定发出文件时要使用的行序列结尾: \"CRLF\" (dos)或 \"LF\" (unix)。", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "指定调试调试程序应将 TypeScript 文件放置到的位置而不是源位置。", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "指定调试程序应将映射文件放置到的位置而不是生成的位置。", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "指定用于从 “node_modules” 检查 JavaScript 文件的最大文件夹深度。仅适用于 “allowJs”。", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "指定要用于从 eg,react 中导入 “jsx” 和 “jsxs” 工厂函数的模块说明符", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "指定为 “createElement” 调用的对象。这仅适用于将 “react” JSX 发出设定为目标的情况。", "Specify_the_output_directory_for_generated_declaration_files_6613": "指定已生成声明文件的输出目录。", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "指定 .tsbuildinfo 增量编译文件的路径。", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "指定输入文件的根目录。与 --outDir 一起用于控制输出目录结构。", "Specify_the_root_folder_within_your_source_files_6690": "指定源文件中的根文件夹。", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "指定调试程序的根路径以查找引用源代码。", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "指定要包含的类型包名称，而无需在源文件中引用。", "Specify_what_JSX_code_is_generated_6646": "指定生成的 JSX 代码。", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "指定当系统耗尽本机文件观察程序时，观察程序应使用的方法。", "Specify_what_module_code_is_generated_6657": "指定生成的模块代码。", "Split_all_invalid_type_only_imports_1367": "拆分所有无效的仅类型导入", "Split_into_two_separate_import_declarations_1366": "拆分为两个单独的导入声明", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "仅当面向 ECMAScript 5 和更高版本时，\"new\" 表达式中的展开运算符才可用。", "Spread_types_may_only_be_created_from_object_types_2698": "spread 类型只能从对象类型创建。", "Starting_compilation_in_watch_mode_6031": "在监视模式下开始编译...", "Statement_expected_1129": "应为语句。", "Statements_are_not_allowed_in_ambient_contexts_1036": "不允许在环境上下文中使用语句。", "Static_members_cannot_reference_class_type_parameters_2302": "静态成员不能引用类类型参数。", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "静态属性“{0}”与构造函数“{1}”的内置属性函数“{0}”冲突。", "String_literal_expected_1141": "应为字符串字面量。", "String_literal_with_double_quotes_expected_1327": "应为带双引号的字符串字面量。", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "使用颜色和上下文风格化错误和消息(实验)。", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "后续属性声明必须属于同一类型。属性“{0}”的类型必须为“{1}”，但此处却为类型“{2}”。", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "后续变量声明必须属于同一类型。变量“{0}”必须属于类型“{1}”，但此处却为类型“{2}”。", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "模式“{1}”的替换“{0}”类型不正确，应为 \"string\"，实际为“{2}”。", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "模式 \"{1}\" 中的替代项 \"{0}\" 最多只能有一个 \"*\" 字符", "Substitutions_for_pattern_0_should_be_an_array_5063": "模式“{0}”的替代应为数组。", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "模式“{0}”的替换模式不应为空数组。", "Successfully_created_a_tsconfig_json_file_6071": "已成功创建 tsconfig.json 文件。", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "不允许在构造函数外部或在构造函数内的嵌套函数中进行 Super 调用。", "Suppress_excess_property_checks_for_object_literals_6072": "取消对对象字面量的多余属性检查。", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "抑制缺少索引签名的索引对象的 noImplicitAny 错误。", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "在对缺少索引签名的对象编制索引时，抑制 “noImplicitAny” 错误。", "Switch_each_misused_0_to_1_95138": "将每个误用的“{0}”切换到“{1}”", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "在不支持本机递归监视的平台上同步调用回调并更新目录观察程序的状态。", "Syntax_Colon_0_6023": "语法: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "标记“{0}”至少需要“{1}”个参数，但 JSX 工厂“{2}”最多可提供“{3}”个。", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "可选链中不允许使用带有标记的模板表达式。", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "目标仅允许 {0} 个元素，但源中的元素可能更多。", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "目标仅允许 {0} 个元素，但源中的元素可能不够。", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "\"{0}\" 修饰符只能在 TypeScript 文件中使用。", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "“{0}”运算符不能应用于类型 \"symbol\"。", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "“{0}”运算符不允许用于布尔类型。请考虑改用“{1}”。", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "异步迭代器的 \"{0}\" 属性必须是方法。", "The_0_property_of_an_iterator_must_be_a_method_2767": "迭代器的 \"{0}\" 属性必须是方法。", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "\"Object\" 类型可分配给极少数其他类型。是否想要改用“任意”类型?", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES3_and_ES5_Consider_using_a_stand_2496": "ES3 和 ES5 中的箭头函数不能引用 \"arguments\" 对象。请考虑使用标准函数表达式。", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES3_and_ES5_Consider_usi_2522": "ES3 和 ES5 中的异步函数或方法不能引用“参数”对象。请考虑使用标准函数或方法。", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "\"if\" 语句的正文不能为空语句。", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "针对此实现的调用已成功，但重载的实现签名在外部不可见。", "The_character_set_of_the_input_files_6163": "输入文件的字符集。", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "包含箭头函数捕获 \"this\" 的全局值。", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "包含函数或模块体对于控制流分析而言太大。", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "当前文件是 CommonJS 模块，因此不能在顶级使用 “await”。", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "当前文件是 CommonJS 模块，其导入将生成“require”调用；但是，引用的文件是 ECMAScript 模块，它不能使用“require”进行导入。请考虑改为编写动态“import(\"{0}\")”调用。", "The_current_host_does_not_support_the_0_option_5001": "当前主机不支持“{0}”选项。", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "你可能打算使用的 \"{0}\" 的声明在此处定义", "The_declaration_was_marked_as_deprecated_here_2798": "该声明曾在此处标记为已弃用。", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "所需类型来自属性 \"{0}\"，在此处的 \"{1}\" 类型上声明该属性", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "所需类型来自此签名的返回类型。", "The_expected_type_comes_from_this_index_signature_6501": "所需类型来自此索引签名。", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "导出分配的表达式必须是环境上下文中的标识符或限定的名称。", "The_file_is_in_the_program_because_Colon_1430": "程序包含该文件是因为:", "The_files_list_in_config_file_0_is_empty_18002": "配置文件“{0}”中的 \"files\" 列表为空。", "The_first_export_default_is_here_2752": "在此处显示第一个导出默认值。", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "承诺的 \"then\" 方法的第一个参数必须是回调。", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "全局类型 \"JSX.{0}\" 不可具有多个属性。", "The_implementation_signature_is_declared_here_2750": "在此处声明实现签名。", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "将生成到 CommonJS 输出的文件中不允许 'import.meta' 元属性。", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "仅当 “--module” 选项为 “es2020”、“es2022”、“esnext”、“system”、“node16” 或 “nodenext” 时，才允许使用 “import.meta” 元属性。", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "如果没有引用 \"{1}\"，则无法命名 \"{0}\" 的推断类型。这很可能不可移植。需要类型注释。", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "推断类型“{0}”引用的类型具有无法简单序列化的循环结构。必须具有类型注释。", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "“{0}”的推断类型引用不可访问的“{1}”类型。需要类型批注。", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "此节点的推断类型超出编译器将序列化的最大长度。需要显式类型注释。", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "由于属性“{1}”存在于多个要素中，但在某些要素中是专用属性，因此已将交集“{0}”缩减为“绝不”。", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "由于属性“{1}”在某些要素中具有存在冲突的类型，因此已将交集“{0}”缩减为“绝不”。", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "\"intrinsic\" 关键字只能用于声明编译器提供的内部类型。", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "必须提供 \"jsxFragmentFactory\" 编译器选项才能将 JSX 片段与 \"jsxFactory\" 编译器选项一起使用。", "The_last_overload_gave_the_following_error_2770": "最后一个重载给出了以下错误。", "The_last_overload_is_declared_here_2771": "在此处声明最后一个重载。", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "\"for...in\" 语句的左侧不能为析构模式。", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "\"for...in\" 语句的左侧不能使用类型批注。", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "\"for…in\" 语句的左侧不能是可选属性访问。", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "\"for...in\" 语句的左侧必须是变量或属性访问。", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "\"for...in\" 语句的左侧必须是 \"string\" 或 \"any\" 类型。", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "\"for...of\" 语句的左侧不能使用类型批注。", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "\"for…of\" 语句的左侧不能是可选属性访问。", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "“for...of” 语句的左侧可能不是 “async”。", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "\"for...of\" 语句的左侧必须是变量或属性访问。", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "算术运算左侧必须是 \"any\"、\"number\"、\"bigint\" 或枚举类型。", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "赋值表达式的左侧不能是可选属性访问。", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "赋值表达式的左侧必须是变量或属性访问。", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "\"instanceof\" 表达式左侧必须是 \"any\" 类型、对象类型或类型参数。", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "向用户显示消息时所用的区域设置(例如，\"en-us\")", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "用于搜索 node_modules 和加载 JavaScript 文件的最大依赖项深度。", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "\"delete\" 运算符的操作数不能是专用标识符。", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "\"delete\" 运算符的操作数不能是只读属性。", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "\"delete\" 运算符的操作数必须是属性引用。", "The_operand_of_a_delete_operator_must_be_optional_2790": "\"delete\" 运算符的操作数必须是可选的。", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "增量或减量运算符的操作数不能是可选属性访问。", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "递增或递减运算符的操作数必须是变量或属性访问。", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "分析器预期在此处找到与“{0}”标记匹配的“{1}”。", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "项目根不明确，但需要解析文件“{1}”中的导出映射项“{0}”。提供 `rootDir` 编译器选项以消除歧义。", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "项目根不明确，但仍需要解析文件“{1}”中的导入映射项“{0}”。提供 `rootDir` 编译器选项以消除歧义。", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "无法在此类中的类型 \"{1}\" 上访问属性 \"{0}\"，因为具有相同拼写的另一个专用标识符隐藏了它。", "The_return_type_of_a_get_accessor_must_be_assignable_to_its_set_accessor_type_2380": "\"Get\" 访问器的返回类型必须可分配给其 \"Set\" 访问器类型", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "参数修饰器函数的返回类型必须为 \"void\" 或 \"any\"。", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "属性修饰器函数的返回类型必须为 \"void\" 或 \"any\"。", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "异步函数的返回类型必须是有效承诺，或不得包含可调用的 \"then\" 成员。 ", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "异步函数或方法的返回类型必须为全局 Promise<T> 类型。你是否是指写入 \"Promise<{0}>\"?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "\"for...in\" 语句右侧必须是 \"any\" 类型、对象类型或类型参数，但此处的类型为“{0}”。", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "算术运算右侧必须是 \"any\"、\"number\"、\"bigint\" 或枚举类型。", "The_right_hand_side_of_an_instanceof_expression_must_be_of_type_any_or_of_a_type_assignable_to_the_F_2359": "\"instanceof\" 表达式的右侧必须属于类型 \"any\"，或属于可分配给 \"Function\" 接口类型的类型。", "The_root_value_of_a_0_file_must_be_an_object_5092": "“{0}”文件的根值必须是一个对象。", "The_shadowing_declaration_of_0_is_defined_here_18017": "在此处定义了“{0}”的阴影声明", "The_signature_0_of_1_is_deprecated_6387": "“{1}”的签名“{0}”已弃用。", "The_specified_path_does_not_exist_Colon_0_5058": "指定的路径不存在:“{0}”。", "The_tag_was_first_specified_here_8034": "第一次在此处指定了标记。", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "对象 rest 分配的目标不能是可选属性访问。", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "对象 rest 分配的目标必须是变量或属性访问。", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "类型为“{0}”的 \"this\" 上下文不能分配给类型为“{1}”的方法的 \"this\"。", "The_this_types_of_each_signature_are_incompatible_2685": "每个签名的 \"this\" 类型不兼容。", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "类型 \"{0}\" 为 \"readonly\"，不能分配给可变类型 \"{1}\"。", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "在将 “export type” 用在其导出语句上时，不能在已命名导出上使用 “type” 修饰符。", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "在将 “import type” 用在其导入语句上时，不能在已命名导入上使用 “type” 修饰符。。", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "函数声明的类型必须与函数的签名匹配。", "The_type_of_this_expression_cannot_be_named_without_a_resolution_mode_assertion_which_is_an_unstable_2841": "如果没有“resolution-mode”断言(这是不稳定的功能)，则无法命名此表达式的类型。请使用夜间 TypeScript 来消除此错误。请尝试使用“npm install -D typescript@next”进行更新。", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "无法序列化此节点的类型，因为无法序列化其属性“{0}”。", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "异步迭代器的 \"{0}()\" 方法返回的类型必须是具有 \"value\" 属性的类型的承诺。", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "迭代器的 \"{0}()\" 方法返回的类型必须具有 \"value\" 属性。", "The_types_of_0_are_incompatible_between_these_types_2200": "在这些类型中，\"{0}\" 的类型不兼容。", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "在这些类型中，\"{0}\" 返回的类型不兼容。", "The_value_0_cannot_be_used_here_18050": "此处不能使用值“{0}”。", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "\"for...in\" 语句的变量声明不能有初始化表达式。", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "\"for...of\" 语句的变量声明不能有初始化表达式。", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "不支持 \"with\" 语句。\"with\" 程序块中的所有符号都将具有类型 \"any\"。", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "此 JSX 标记的 \"{0}\" 属性需要 \"{1}\" 类型的子级，但提供了多个子级。", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "此 JSX 标记的 \"{0}\" 属性需要类型 \"{1}\"，该类型需要多个子级，但仅提供了一个子级。", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "此比较似乎是无意的，因为类型“{0}”和“{1}”没有重叠。", "This_condition_will_always_return_0_2845": "此条件将始终返回“{0}”。", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "此条件将始终返回“{0}”，因为 JavaScript 按引用而不是值比较对象。", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "此条件将始终返回 true，因为此“{0}”已始终定义。", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "此条件将始终返回 true，因为始终定义了函数。你是想改为调用它吗?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "此构造函数可能会转换为类声明。", "This_expression_is_not_callable_2349": "此表达式不可调用。", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "此表达式是 \"get\" 访问器，因此不可调用。你想在不使用 \"()\" 的情况下使用它吗?", "This_expression_is_not_constructable_2351": "此表达式不可构造。", "This_file_already_has_a_default_export_95130": "此文件已具有默认导出", "This_import_is_never_used_as_a_value_and_must_use_import_type_because_importsNotUsedAsValues_is_set__1371": "此导入从不用作值，必须使用 \"import type\" ，因为 \"importsNotUsedAsValues\" 设置为 \"error\"。", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "这是正在扩充的声明。请考虑将扩充声明移到同一个文件中。", "This_may_be_converted_to_an_async_function_80006": "可将此转换为异步函数。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "此成员不能具有带 “@override” 标记的 JSDoc 注释，因为未在基类“{0}”中对其进行声明。", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "此成员不能具有带 “override” 标记的 JSDoc 注释，因为未在基类“{0}”中对其进行声明。你是否指的是“{1}”?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "此成员不能具有带 “@override” 标记的 JSDoc 注释，因为所包含的类“{0}”不会扩展其他类。", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "此成员不能有 \"override\" 修饰符，因为它未在基类 \"{0}\" 中声明。", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "此成员不能有 “override” 修饰符，因为它未在基类“{0}”中声明。你是否指的是“{1}”?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "此成员不能有 \"override\" 修饰符，因为它的包含类 \"{0}\" 不扩展其他类。", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "此成员必须具有带 “@override” 标记的 JSDoc 注释，因为它会替代基类“{0}”中的成员。", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "此成员必须有 \"override\" 修饰符，因为它替代基类 \"{0}\" 中的一个成员。", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "此成员必须有 \"override\" 修饰符，因为它替代基类 \"{0}\" 中声明的一个抽象方法。", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "只能通过启用 \"{0}\" 标志并引用其默认导出，使用 ECMAScript 导入/导出来引用此模块。", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "此模块是使用 “export =” 声明的，只能在使用“{0}”标志时用于默认导入。", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "此重载签名与其实现签名不兼容。", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "此参数不允许与 \"use strict\" 指令一起使用。", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "此参数属性必须具有带 “@override” 标记的 JSDoc 注释，因为它将替代基类“{0}”中的成员。", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "此参数属性必须具有 “override” 修饰符，因为它会替代基类“{0}”中的成员。", "This_spread_always_overwrites_this_property_2785": "此扩张将始终覆盖此属性。", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "此语法保留在扩展名为 .mts 或 .cts 的文件中。请添加尾随逗号或显式约束。", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "此语法保留在扩展名为 .mts 或 .cts 的文件中。请改用 `as` 表达式。", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "此语法需要一个导入的帮助程序，但找不到模块“{0}”。", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "此语法需要名为 \"{1}\" 的导入帮助器，\"{0}\" 中不存在该帮助器。请考虑升级 \"{0}\" 的版本。", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "此语法需要一个名为 \"{1}\" 且包含 {2} 参数的导入帮助程序，该帮助程序与 \"{0}\" 中的相应帮助程序不兼容。请考虑升级 \"{0}\" 的版本。", "This_type_parameter_might_need_an_extends_0_constraint_2208": "此类型参数可能需要 `extends {0}` 约束。", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "“import” 的这种用法无效。可以写入 “import()” 调用，但它们必须具有括号，并且不能带有类型参数。", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "若要将此文件转换为 ECMAScript 模块，请将字段“\"type\": \"module\"”添加到“{0}”。", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "若要将此文件转换为 ECMAScript 模块，请将其文件扩展名更改为“{0}”，或将字段“\"type\": \"module\"”添加到“{1}”。", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "若要将此文件转换为 ECMAScript 模块，请将其文件扩展名更改为“{0}”，或者使用“{ \"type\": \"module\" }”创建本地 package.json 文件。", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "若要将此文件转换为 ECMAScript 模块，请使用“{ \"type\": \"module\" }”创建本地 package.json 文件。", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "仅当 “module” 选项设置为 “es2022”、“esnext”、“system”、“node16” 或 “nodenext”，且 “target” 选项设置为 “es2017” 或更高版本时，才允许使用顶级 “await” 表达式。", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": ".d.ts 文件中的顶级声明必须以 \"declare\" 或 \"export\" 修饰符开头。", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "仅当 “module” 选项设置为 “es2022”、“esnext”、“system”、“node16” 或 “nodenext”，且 “target” 选项设置为 “es2017” 或更高版本时，才允许使用顶级 “for await” 循环。", "Trailing_comma_not_allowed_1009": "不允许使用尾随逗号。", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "将每个文件转换为单独的模块(类似 \"ts.transpileModule\")。", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "尝试使用 `npm i --save-dev @types/{1}` (如果存在)，或者添加一个包含 `declare module '{0}';` 的新声明(.d.ts)文件", "Trying_other_entries_in_rootDirs_6110": "正在尝试 \"rootDirs\" 中的其他条目。", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "正在尝试替换“{0}”，候选模块位置:“{1}”。", "Tuple_members_must_all_have_names_or_all_not_have_names_5084": "元组成员必须全部具有或全部不具有名称。", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "长度为 \"{1}\" 的元组类型 \"{0}\" 在索引 \"{2}\" 处没有元素。", "Tuple_type_arguments_circularly_reference_themselves_4110": "元组类型参数循环引用自身。", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "只有在使用 \"--downlevelIteration\" 标志或 \"--target\" 为 \"es2015\" 或更高版本时，才能循环访问类型“{0}”。", "Type_0_cannot_be_used_as_an_index_type_2538": "类型“{0}”不能作为索引类型使用。", "Type_0_cannot_be_used_to_index_type_1_2536": "类型“{0}”无法用于索引类型“{1}”。", "Type_0_does_not_satisfy_the_constraint_1_2344": "类型“{0}”不满足约束“{1}”。", "Type_0_does_not_satisfy_the_expected_type_1_1360": "类型“{0}”不满足预期类型“{1}”。", "Type_0_has_no_call_signatures_2757": "类型 \"{0}\" 没有调用签名。", "Type_0_has_no_construct_signatures_2761": "类型 \"{0}\" 没有构造签名。", "Type_0_has_no_matching_index_signature_for_type_1_2537": "类型“{0}”没有匹配的类型“{1}”的索引签名。", "Type_0_has_no_properties_in_common_with_type_1_2559": "类型“{0}”与类型“{1}”不具有相同的属性。", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "类型“{0}”没有类型参数列表适用的签名。", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "类型“{0}”缺少类型“{1}”中的以下属性: {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "类型“{0}”缺少类型“{1}”的以下属性: {2} 及其他 {3} 项。", "Type_0_is_not_a_constructor_function_type_2507": "类型“{0}”不是构造函数类型。", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_SlashES3_because_it_does_not_refer_to_a_Prom_1055": "类型“{0}”不是 ES5/ES3 中的有效异步函数返回类型，因为其未引用与 Promise 相符的构造函数值。", "Type_0_is_not_an_array_type_2461": "类型“{0}”不是数组类型。", "Type_0_is_not_an_array_type_or_a_string_type_2495": "类型“{0}”不是数组类型或字符串类型。", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "类型“{0}”不是数组类型或字符串类型，或者没有返回迭代器的 \"[Symbol.iterator]()\" 方法。", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "类型“{0}”不是数组类型，或者没有返回迭代器的 \"[Symbol.iterator]()\" 方法。", "Type_0_is_not_assignable_to_type_1_2322": "不能将类型“{0}”分配给类型“{1}”。", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "类型“{0}”不可分配给类型“{1}”。你的意思是“{2}”?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "类型“{0}”无法分配给类型“{1}”。存在具有此名称的两种不同类型，但它们是不相关的。", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "类型“{0}”不能分配给类型“{1}”，如方差批注所示。", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "类型 “{0}” 不能分配给“exactOptionalPropertyTypes: true”的类型 “{1}”。请考虑将 “undefined” 添加到目标属性的类型。", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "类型 “{0}” 不能分配给“exactOptionalPropertyTypes: true”的类型 “{1}”。请考虑将 “undefined” 添加到目标类型。。", "Type_0_is_not_comparable_to_type_1_2678": "类型“{0}”不可与类型“{1}”进行比较。", "Type_0_is_not_generic_2315": "类型“{0}”不是泛型类型。", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "类型 \"{0}\" 可以表示基元值，该值不允许作为“in”运算符的右操作数。", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "类型“{0}”必须具有返回异步迭代器的 \"[Symbol.asyncIterator]()\" 方法。", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "类型“{0}”必须具有返回迭代器的 \"[Symbol.iterator]()\" 方法。", "Type_0_provides_no_match_for_the_signature_1_2658": "类型“{0}”提供的内容与签名“{1}”不匹配。", "Type_0_recursively_references_itself_as_a_base_type_2310": "类型“{0}”以递归方式将自身引用为基类。", "Type_Checking_6248": "类型检查", "Type_alias_0_circularly_references_itself_2456": "类型别名“{0}”循环引用自身。", "Type_alias_must_be_given_a_name_1439": "必须为类型别名指定名称。", "Type_alias_name_cannot_be_0_2457": "类型别名不能为“{0}”。", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "类型别名只能在 TypeScript 文件中使用。", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "类型批注不能出现在构造函数声明中。", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "类型注释只能在 TypeScript 文件中使用。", "Type_argument_expected_1140": "应为类型参数。", "Type_argument_list_cannot_be_empty_1099": "类型参数列表不能为空。", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "类型参数只能在 TypeScript 文件中使用。", "Type_arguments_cannot_be_used_here_1342": "无法在此处使用类型参数。", "Type_arguments_for_0_circularly_reference_themselves_4109": "\"{0}\" 的类型参数循环引用自身。", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "类型断言表达式只能在 TypeScript 文件中使用。", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "源中位置 {0} 的类型与目标中位置 {1} 的类型不兼容。", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "源中位置 {0} 到 {1} 的类型与目标中位置 {2} 的类型不兼容。", "Type_declaration_files_to_be_included_in_compilation_6124": "要包含在编译中类型声明文件。", "Type_expected_1110": "应为类型。", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "类型导入断言应恰好有一个键 - \"resolution-mode\" - 值为 \"import\" 或 \"require\"。", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "类型实例化过深，且可能无限。", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "类型在其自身的 \"then\" 方法的 fulfillment 回调中被直接或间接引用。", "Type_library_referenced_via_0_from_file_1_1402": "通过 \"{0}\" 从文件 \"{1}\" 引用了库类型", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "通过 \"{0}\" 从具有 packageId \"{2}\" 的文件 \"{1}\" 引用了库类型", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "\"await\" 操作数的类型必须是有效承诺，或不得包含可调用的 \"then\" 成员。", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "计算属性类型的值为 \"{0}\"，该值不能赋给 \"{1}\" 类型。", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "实例成员变量“{0}”的类型不能引用构造函数中声明的标识符“{1}”。", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "\"yield*\" 操作数的迭代元素的类型必须是有效承诺，或不得包含可调用的 \"then\" 成员。", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "属性“{0}”的类型在已映射的类型“{1}”中循环引用其自身。", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "异步生成器中 \"yield\" 操作数的类型必须是有效承诺，或不得包含可调用的 \"then\" 成员。", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "此导入产生的类型。无法调用或构造命名空间样式的导入，这类导入将在运行时导致失败。请考虑改为使用默认导入或此处需要的导入。", "Type_parameter_0_has_a_circular_constraint_2313": "类型参数“{0}”具有循环约束。", "Type_parameter_0_has_a_circular_default_2716": "类型参数“{0}”具有循环默认值。", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "导出接口中的调用签名的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "导出接口中的构造函数签名的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "导出类的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "导出函数的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "导出接口的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "已导出映射对象类型的类型参数 \"{0}\" 使用专用名称 \"{1}\" 。", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "已导出类型别名的类型参数“{0}”具有或正使用专用名称“{1}”。", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "导出接口中的方法的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "导出类中的公共方法的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "导出类中的公共静态方法的类型参数“{0}”具有或正在使用专用名称“{1}”。", "Type_parameter_declaration_expected_1139": "应为类型参数声明。", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "类型参数声明只能在 TypeScript 文件中使用。", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "类型参数默认值只能引用以前声明的类型参数。", "Type_parameter_list_cannot_be_empty_1098": "类型参数列表不能为空。", "Type_parameter_name_cannot_be_0_2368": "类型参数名称不能为“{0}”。", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "类型参数不能出现在构造函数声明中。", "Type_predicate_0_is_not_assignable_to_1_1226": "类型谓词“{0}”不可分配给“{1}”。", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "类型生成的元组类型太大，无法表示。", "Type_reference_directive_0_was_not_resolved_6120": "======== 未解析类型引用指令“{0}”。========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== 类型引用指令“{0}”已成功解析为“{1}”，主要: {2}。========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== 类型引用指令 \"{0}\" 已成功解析为 \"{1}\" ，包 ID 为 \"{2}\"，主要: {3}。========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "类型满意度表达式只能在 TypeScript 文件中使用。", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "类型不能出现在 JavaScript 文件的导出声明中。", "Types_have_separate_declarations_of_a_private_property_0_2442": "类型具有私有属性“{0}”的单独声明。", "Types_of_construct_signatures_are_incompatible_2419": "构造签名的类型不兼容。", "Types_of_parameters_0_and_1_are_incompatible_2328": "参数“{0}”和“{1}” 的类型不兼容。", "Types_of_property_0_are_incompatible_2326": "属性“{0}”的类型不兼容。", "Unable_to_open_file_0_6050": "无法打开文件“{0}”。", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "作为表达式调用时，无法解析类修饰器的签名。", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "作为表达式调用时，无法解析方法修饰器的签名。", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "作为表达式调用时，无法解析参数修饰器的签名。", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "作为表达式调用时，无法解析属性修饰器的签名。", "Unexpected_end_of_text_1126": "文本意外结束。", "Unexpected_keyword_or_identifier_1434": "意外的关键字或标识符。", "Unexpected_token_1012": "意外的标记。", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "意外的标记。应为构造函数、方法、访问器或属性。", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "意外的标记。类型参数名不应包含大括号。", "Unexpected_token_Did_you_mean_or_gt_1382": "意外的标记。你是想使用 `{'>'}` 还是 `>`?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "意外的标记。你是想使用 `{'}'}` 还是 `&rbrace;`?", "Unexpected_token_expected_1179": "意外标记。应为 \"{\"。", "Unknown_build_option_0_5072": "未知的生成选项 \"{0}\"。", "Unknown_build_option_0_Did_you_mean_1_5077": "未知的生成选项 \"{0}\"。你是想使用 \"{1}\" 吗?", "Unknown_compiler_option_0_5023": "未知的编译器选项“{0}”。", "Unknown_compiler_option_0_Did_you_mean_1_5025": "未知的编译器选项 \"{0}\"。你是想使用 \"{1}\" 吗?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "未知的关键字或标识符。你是不是指“{0}”?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "未知的 \"excludes\" 选项。你的意思是 \"exclude\"?", "Unknown_type_acquisition_option_0_17010": "未知类型获取选项“{0}”。", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "未知的类型获取选项 \"{0}\"。你是想使用 \"{1}\" 吗?", "Unknown_watch_option_0_5078": "未知观察选项 \"{0}\"。", "Unknown_watch_option_0_Did_you_mean_1_5079": "未知的监视选项 \"{0}\"。你是想使用 \"{1}\" 吗?", "Unreachable_code_detected_7027": "检测到无法访问的代码。", "Unterminated_Unicode_escape_sequence_1199": "未终止的 Unicode 转义序列。", "Unterminated_quoted_string_in_response_file_0_6045": "响应文件“{0}”中引号不配对。", "Unterminated_regular_expression_literal_1161": "未终止的正则表达式字面量。", "Unterminated_string_literal_1002": "未终止的字符串字面量。", "Unterminated_template_literal_1160": "未终止的模板字面量。", "Untyped_function_calls_may_not_accept_type_arguments_2347": "非类型化函数调用不能接受类型参数。", "Unused_label_7028": "未使用的标签。", "Unused_ts_expect_error_directive_2578": "未使用的 \"@ts-expect-error\" 指令。", "Update_import_from_0_90058": "从“{0}”更新导入", "Updating_output_of_project_0_6373": "正在更新项目 \"{0}\" 的输出…", "Updating_output_timestamps_of_project_0_6359": "正在更新项目“{0}”的输出时间戳...", "Updating_unchanged_output_timestamps_of_project_0_6371": "正在更新项目 \"{0}\" 未更改的输出时间戳…", "Use_0_95174": "使用 `{0}`", "Use_Number_isNaN_in_all_conditions_95175": "在所有条件下使用 `Number.isNaN`。", "Use_element_access_for_0_95145": "对“{0}”使用元素访问", "Use_element_access_for_all_undeclared_properties_95146": "对所有未声明的属性使用元素访问。", "Use_synthetic_default_member_95016": "使用综合的“默认”成员。", "Using_0_subpath_1_with_target_2_6404": "将“{0}”子路径“{1}”与目标“{2}”一起使用", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "仅 ECMAScript 5 和更高版本支持在 \"for...of\" 语句中使用字符串。", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "使用 --build，-b 将使 tsc 的行为更像生成业务流程协调程序，而非编译器。这可用于触发生成复合项目，你可以在 {0} 详细了解这些项目", "Using_compiler_options_of_project_reference_redirect_0_6215": "使用项目引用重定向“{0}”的编译器选项。", "VERSION_6036": "版本", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "类型“{0}”的值没有与类型“{1}”相同的属性。你是想调用它吗?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "类型“{0}”的值不可调用。是否希望包括 \"new\"?", "Variable_0_implicitly_has_an_1_type_7005": "变量“{0}”隐式具有“{1}”类型。", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "变量 \"{0}\" 隐式具有 \"{1}\" 类型，但可以从用法中推断出更好的类型。", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "变量 \"{0}\" 在某些位置隐式具有类型 \"{1}\"，但可以从使用情况推断出更好的类型。", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "变量“{0}”在某些无法确定其类型的位置处隐式具有类型“{1}”。", "Variable_0_is_used_before_being_assigned_2454": "在赋值前使用了变量“{0}”。", "Variable_declaration_expected_1134": "应为变量声明。", "Variable_declaration_list_cannot_be_empty_1123": "变量声明列表不能为空。", "Variable_declaration_not_allowed_at_this_location_1440": "此位置不允许使用变量声明。", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "源中位置 {0} 的可变元素与目标中位置 {1} 的元素不匹配。", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "仅对象、函数、构造函数、映射类型的类型别名支持方差注释。", "Version_0_6029": "版本 {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "请访问 https://aka.ms/tsconfig，了解有关此文件的详细信息", "WATCH_OPTIONS_6918": "监视选项", "Watch_and_Build_Modes_6250": "观看和生成模式", "Watch_input_files_6005": "监视输入文件。", "Watch_option_0_requires_a_value_of_type_1_5080": "观察选项 \"{0}\" 需要 {1} 类型的值。", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "我们只能通过在此处为整个参数添加类型来写入“{0}”的类型。", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "分配函数时，请检查以确保参数和返回值与子类型兼容。", "When_type_checking_take_into_account_null_and_undefined_6699": "进行类型检查时，请考虑 “null” 和 “undefined”。", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "是否在监视模式下保留过时的控制台输出，而不是清除屏幕。", "Wrap_all_invalid_characters_in_an_expression_container_95109": "包装表达式容器中的所有无效字符", "Wrap_all_object_literal_with_parentheses_95116": "用括号括起所有对象字面量", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "将所有没有父级的 JSX 包装在 JSX 片段中", "Wrap_in_JSX_fragment_95120": "包装在 JSX 片段中", "Wrap_invalid_character_in_an_expression_container_95108": "包装表达式容器中的无效字符", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "用括号括起以下应为对象字面量的内容", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "你可以在 {0} 了解编译器选项的所有内容", "You_cannot_rename_a_module_via_a_global_import_8031": "不能通过全局导入对模块进行重命名。", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "不能重命名已在 “node_modules” 文件夹中定义的元素。", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "不能重命名已在另一个 “node_modules” 文件夹中定义的元素。", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "不能重命名标准 TypeScript 库中定义的元素。", "You_cannot_rename_this_element_8000": "无法重命名此元素。", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "“{0}”收到的参数过少，无法在此处充当修饰器。你是要先调用它，然后再写入 \"@{0}()\" 吗?", "_0_and_1_index_signatures_are_incompatible_2330": "“{0}”和“{1}”索引签名不兼容。", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "不能在不使用括号的情况下混用 \"{0}\" 和 \"{1}\" 操作。", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "“{0}”被指定了两次。将覆盖名为“{0}”的特性。", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "只能通过启用 \"esModuleInterop\" 标志并使用默认导入来导入“{0}”。", "_0_can_only_be_imported_by_using_a_default_import_2595": "仅可使用默认导入来导入“{0}”。", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "只能通过使用 \"require\" 调用或启用 \"esModuleInterop\" 标志并使用默认导入来导入“{0}”。", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "只能使用 \"require\" 调用或使用默认导入来导入“{0}”。", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "仅可使用 \"import {1} = require({2})\" 或默认导入来导入“{0}”。", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "仅可使用 \"import {1} = require({2})\" 或通过启用 \"esModuleInterop\" 标志并使用默认导入来导入“{0}”。", "_0_cannot_be_compiled_under_isolatedModules_because_it_is_considered_a_global_script_file_Add_an_imp_1208": "无法在 \"--isolatedModules\" 下编译“{0}”，因为它被视为全局脚本文件。请添加导入、导出或空的 \"export {}\" 语句来使它成为模块。", "_0_cannot_be_used_as_a_JSX_component_2786": "“{0}”不能用作 JSX 组件。", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "\"{0}\" 是使用 \"export type\" 导出的，因此不能用作值。", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "\"{0}\" 是使用 \"import type\" 导入的，因此不能用作值。", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "\"{0}\" 组件不接受文本作为子元素。JSX 中的文本类型为 \"string\"，但 \"{1}\" 的预期类型为 \"{2}\"。", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "“{0}”可以使用与“{1}”无关的任意类型进行实例化。", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "\"{0}\" 声明只能在 TypeScript 文件中使用。", "_0_expected_1005": "应为“{0}”。", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "“{0}”没有导出的成员“{1}”。你是否指的是“{2}”?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "\"{0}\" 隐式具有 \"{1}\" 返回类型，但可以从用法中推断出更好的类型。", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "由于“{0}'”不具有返回类型批注并且在它的一个返回表达式中得到直接或间接引用，因此它隐式具有返回类型 \"any\"。", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "“{0}”隐式具有类型 \"any\"，因为它不具有类型批注，且在其自身的初始化表达式中得到直接或间接引用。", "_0_index_signatures_are_incompatible_2634": "“{0}”索引签名不兼容。", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "“{0}”索引类型“{1}”不能分配给“{2}”索引类型“{3}”。", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "“{0}”是基元，但“{1}”是包装器对象。如可能首选使用“{0}”。", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "“{0}”是一种类型，无法在 JavaScript 文件中导入。请在 JSDoc 类型批注中使用“{1}”。", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_preserveValueImports_and_isolatedMod_1444": "\"{0}\" 是一种类型，在同时启用了 \"preserveValueImports\" 和 \"isolatedModules\" 时，必须使用仅类型导入进行导入。", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "“{0}”是“{1}”的未使用重命名。是否打算将其用作类型批注?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "\"{0}\" 可赋给 \"{1}\" 类型的约束，但可以使用约束 \"{2}\" 的其他子类型实例化 \"{1}\"。", "_0_is_automatically_exported_here_18044": "“{0}”自动导出到此处。", "_0_is_declared_but_its_value_is_never_read_6133": "已声明“{0}”，但从未读取其值。", "_0_is_declared_but_never_used_6196": "“{0}”已声明，但从未使用过。", "_0_is_declared_here_2728": "在此处声明了 \"{0}\"。", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "\"{0}\" 在类 \"{1}\" 中定义为属性，但这里在 \"{2}\" 中重写为访问器。", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "\"{0}\" 在类 \"{1}\" 中定义为访问器，但这里在 \"{2}\" 中重写为实例属性。", "_0_is_deprecated_6385": "“{0}”已弃用。", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "“{0}”不是关键字“{1}”的有效元属性。是否是指“{2}”?", "_0_is_not_allowed_as_a_parameter_name_1390": "不允许将 '{0}' 作为参数名。", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "“{0}”不得用作变量声明名称。", "_0_is_of_type_unknown_18046": "“{0}”的类型为“未知”。", "_0_is_possibly_null_18047": "“{0}”可能为 “null”。", "_0_is_possibly_null_or_undefined_18049": "{0}可能为 “null” 或“未定义”。", "_0_is_possibly_undefined_18048": "“{0}”可能为“未定义”。", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "“{0}”在其自身的基表达式中得到直接或间接引用。", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "“{0}”在其自身的类型批注中得到直接或间接引用。", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "多次指定了 \"{0}\"，因此将重写此用法。", "_0_list_cannot_be_empty_1097": "“{0}”列表不能为空。", "_0_modifier_already_seen_1030": "已看到“{0}”修饰符。", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "“{0}”修饰符只能出现在类、接口或类型别名的类型参数上", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "“{0}”修饰符不能出现在构造函数声明中。", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "“{0}”修饰符不可出现在模块或命名空间元素上。", "_0_modifier_cannot_appear_on_a_parameter_1090": "“{0}”修饰符不能出现在参数中。", "_0_modifier_cannot_appear_on_a_type_member_1070": "“{0}”修饰符不可出现在类型成员上。", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "“{0}”修饰符不能出现在类型参数上", "_0_modifier_cannot_appear_on_an_index_signature_1071": "“{0}”修饰符不可出现在索引签名上。", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "“{0}”修饰符不能出现在此类型的类元素上。", "_0_modifier_cannot_be_used_here_1042": "“{0}”修饰符不能在此处使用。", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "“{0}”修饰符不能在环境上下文中使用。", "_0_modifier_cannot_be_used_with_1_modifier_1243": "“{0}”修饰符不能与“{1}”修饰符一起使用。", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "“{0}”修饰符不能与专用标识符一起使用。", "_0_modifier_must_precede_1_modifier_1029": "“{0}”修饰符必须位于“{1}”修饰符之前。", "_0_needs_an_explicit_type_annotation_2782": "\"{0}\" 需要显式类型注释。", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "“{0}”仅指类型，但在此用作命名空间。", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "“{0}”仅表示类型，但在此处却作为值使用。", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "“{0}”仅引用一个类型，但在此处用作一个值。你是否想要使用“{0} 中的 {1}”?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "“{0}” 仅指类型，但在此处用作值。是否需要更改目标库? 请尝试将 “lib” 编译器选项更改为 es2015 或更高版本。", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "“{0}”指 UMD 全局，但当前文件是模块。请考虑改为添加导入。", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "“{0}”表示值，但在此处用作类型。是否指“类型 {0}”?", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_preserveVa_1446": "\"{0}\" 解析为仅类型声明，并且在同时启用了 \"preserveValueImports\" 和 \"isolatedModules\" 时，必须使用仅类型导入进行导入。", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_isol_1448": "\"{0}\" 解析为仅类型声明，并且在启用 \"isolatedModules\" 时必须使用仅类型重新导出进行重新导出。", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "应在 config json 文件的 “compilerOptions” 对象中设置 “{0}”", "_0_tag_already_specified_1223": "已指定“{0}”标记。", "_0_was_also_declared_here_6203": "此处也声明了 \"{0}\"。", "_0_was_exported_here_1377": "在此处导出了 \"{0}\"。", "_0_was_imported_here_1376": "此处导入了 \"{0}\"。", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "缺少返回类型批注的“{0}”隐式具有“{1}”返回类型。", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "缺少返回类型批注的 \"{0}\" 隐式具有 \"{1}\" 产出类型。", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "\"abstract\" 修饰符仅可出现在类、方法或属性声明中。", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "\"accessor\" 修饰符只能出现在属性声明中。", "and_here_6204": "并在这里。", "arguments_cannot_be_referenced_in_property_initializers_2815": "无法在属性初始化表达式中引用 \"arguments\"。", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "“auto”: 将带有导入、导出、import.meta、jsx (带有 jsx: react-jsx)或 esm 格式(带模块: node16+)的文件视为模块。", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "仅当文件是模块时，才允许在该文件的顶层使用 \"await\" 表达式，但此文件没有导入或导出。请考虑添加空的 \"export {}\" 以将此文件变为模块。", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "仅允许在异步函数和模块顶级使用 \"await\" 表达式。", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "不能在参数初始化表达式中使用 \"await\" 表达式。", "await_has_no_effect_on_the_type_of_this_expression_80007": "\"await\" 对此表达式的类型没有影响。", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "\"baseUrl\" 选项设置为“{0}”，可使用此值解析非相关模块名称“{1}”。", "can_only_be_used_at_the_start_of_a_file_18026": "\"#!\" 只能用在文件的开头。", "case_or_default_expected_1130": "应为 \"case\" 或 \"default\"。", "catch_or_finally_expected_1472": "应为 “catch” 或 “finally”。", "const_declarations_can_only_be_declared_inside_a_block_1156": "\"const\" 声明只能在块的内部声明。", "const_declarations_must_be_initialized_1155": "必须初始化 \"const\" 声明。", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "\"const\" 枚举成员初始化表达式的求值结果为非有限值。", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "\"const\" 枚举成员初始化表达式的求值结果为不允许使用的值 \"NaN\"。", "const_enum_member_initializers_can_only_contain_literal_values_and_other_computed_enum_values_2474": "常量枚举成员初始值设定项只能包含字面量值和其他计算的枚举值。", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "\"const\" 枚举仅可在属性、索引访问表达式、导入声明的右侧、导出分配或类型查询中使用。", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "“构造函数”不能用作参数属性名称。", "constructor_is_a_reserved_word_18012": "\"#constructor\" 是保留字。", "default_Colon_6903": "默认值:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "在严格模式下，无法对标识符调用 \"delete\"。", "export_Asterisk_does_not_re_export_a_default_1195": "\"export *\" 不会重新导出默认值。", "export_can_only_be_used_in_TypeScript_files_8003": "\"export =\" 只能在 TypeScript 文件中使用。", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "\"export\" 修饰符不可用于环境模块和模块扩大，因为它们始终可见。", "extends_clause_already_seen_1172": "已看到 \"extends\" 子句。", "extends_clause_must_precede_implements_clause_1173": "\"extends\" 子句必须位于 \"implements\" 子句之前。", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "导出的类“{0}”的 \"extends\" 子句具有或正在使用专用名称“{1}”。", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "导出的类的 \"extends\" 子句具有或正在使用专用名称“{0}”。", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "导出接口“{0}”的 \"extends\" 子句具有或正在使用专用名称“{1}”。", "false_unless_composite_is_set_6906": "\"false\"，除非设置了 \"composite\"", "false_unless_strict_is_set_6905": "\"false\"，除非设置了 \"strict\"", "file_6025": "文件", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "仅当文件是模块且没有导入或导出项时，才允许在该文件的顶层使用“for await”循环。可考虑添加空的“export {}”将此文件变为模块。", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "仅允许在异步函数和模块顶层使用“for await”循环。", "get_and_set_accessors_cannot_declare_this_parameters_2784": "\"get\" 和 \"set\" 访问器无法声明 \"this\" 参数。", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "如果指定了 \"files\"，则为 \"[]\"，否则为\"[\"**/*\"]5D;\"", "implements_clause_already_seen_1175": "已看到 \"implements\" 子句。", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "\"implements\" 子句只能在 TypeScript 文件中使用。", "import_can_only_be_used_in_TypeScript_files_8002": "\"import ... =\" 只能在 TypeScript 文件中使用。", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "仅条件类型的 \"extends\" 子句中才允许 \"infer\" 声明。", "let_declarations_can_only_be_declared_inside_a_block_1157": "\"let\" 声明只能在块的内部声明。", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "\"let\" 不能用作 \"let\" 或 \"const\" 声明中的名称。", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "module === 'AMD' 或 'UMD' 或 'System' 或 'ES6'，然后 'Classic'， 否则为 'Node'", "module_system_or_esModuleInterop_6904": "module === \"system\" 或 esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "其目标缺少构造签名的 \"new\" 表达式隐式具有 \"any\" 类型。", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "`[\"node_modules\"、\"bower_components\"、\"jspm_packages\"]`，以及 \"outDir\" 的值(如果指定)。", "one_of_Colon_6900": "以下其中一个:", "one_or_more_Colon_6901": "一个或更多:", "options_6024": "选项", "or_JSX_element_expected_1145": "应为 “{” 或 JSX 元素。", "or_expected_1144": "应为 \"{\" 或 \";\"。", "package_json_does_not_have_a_0_field_6100": "\"package.json\" 没有“{0}”字段。", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "\"package. json\" 没有与版本 \"{0}\" 匹配的 \"typesVersions\" 项。", "package_json_had_a_falsy_0_field_6220": "\"package. json\" 具有错误的 \"{0}\" 字段。", "package_json_has_0_field_1_that_references_2_6101": "\"package.json\" 具有引用“{2}”的“{0}”字段“{1}”。", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "\"package. json\" 具有 \"typesVersions\" 项 \"{0}\"，它不是有效的 semver 范围。", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "\"package. json\" 具有与编译器版本 \"{1}\" 匹配的 \"typesVersions\" 项 \"{0}\"，它需要与模块名称 \"{2}\" 匹配的模式。", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "\"package. json\" 具有 \"typesVersions\" 字段，它具有特定于版本的路径映射。", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "package.json 作用域 '{0}' 将说明符 '{1}' 显式映射到 NULL。", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "package.json 作用域 '{0}' 的说明符 '{1}' 的目标类型无效", "package_json_scope_0_has_no_imports_defined_6273": "package.json 作用域 '{0}' 未定义导入。", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "指定了 \"paths“ 选项，正在查找模式以匹配模块名“{0}”。", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "\"readonly\" 修饰符仅可出现在属性声明或索引签名中。", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "仅允许对数组和元组字面量类型使用 \"readonly\" 类型修饰符。", "require_call_may_be_converted_to_an_import_80005": "可将 \"require\" 调用转换为 import 语句。", "resolution_mode_assertions_are_only_supported_when_moduleResolution_is_node16_or_nodenext_1452": "仅当“moduleResolution”为“node16”或“nodenext”时才支持“resolution-mode”断言。", "resolution_mode_assertions_are_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_wi_4125": "“resolution-mode”断言不稳定。请使用夜间 TypeScript 消除此错误。请尝试使用“npm install -D typescript@next”进行更新。", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "只能为仅类型导入设置 \"resolution-mode\"。", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "\"resolution-mode\" 是类型导入断言的唯一有效密钥。", "resolution_mode_should_be_either_require_or_import_1453": "“resolution-mode”应为“require”或“import”。", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "设置了 \"rootDirs\" 选项，可将其用于解析相对模块名称“{0}”。", "super_can_only_be_referenced_in_a_derived_class_2335": "只能在派生类中引用 \"super\"。", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "仅可在派生类或对象字面量表达式的成员中引用 \"super\"。", "super_cannot_be_referenced_in_a_computed_property_name_2466": "不能在计算属性名中引用 \"super\"。", "super_cannot_be_referenced_in_constructor_arguments_2336": "不能在构造函数参数中引用 \"super\"。", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "\"target\" 选项为 \"ES2015\" 或更高版本时，仅对象字面量表达式的成员中允许 \"super\"。", "super_may_not_use_type_arguments_2754": "\"super\" 不能使用类型参数。", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "访问派生类构造函数中的 \"super\" 属性前，必须调用 \"super\"。", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "访问派生类的构造函数中的 \"this\" 前，必须调用 \"super\"。", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "\"super\" 的后面必须是参数列表或成员访问。", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "只有构造函数、成员函数或派生类的成员访问器中才允许 \"super\" 属性访问。", "this_cannot_be_referenced_in_a_computed_property_name_2465": "不能在计算属性名中引用 \"this\"。", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "不能在模块或命名空间体中引用 \"this\"。", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "不能在静态属性初始化表达式中引用 \"this\"。", "this_cannot_be_referenced_in_constructor_arguments_2333": "不能在构造函数参数中引用 \"this\"。", "this_cannot_be_referenced_in_current_location_2332": "不能在当前位置引用 \"this\"。", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "\"this\" 隐式具有类型 \"any\"，因为它没有类型注释。", "true_for_ES2022_and_above_including_ESNext_6930": "对于 ES2022 及更高版本为 `true`，包括 ESNext。", "true_if_composite_false_otherwise_6909": "如果为 \"composite\"，则为 \"true\"，否则为 \"false\"", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: TypeScript 编译器", "type_Colon_6902": "类型:", "unique_symbol_types_are_not_allowed_here_1335": "此处不允许使用 \"unique symbol\" 类型。", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "\"unique symbol\" 类型仅可用于变量语句中的变量。", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "不可在具有绑定名称的变量声明中使用 \"unique symbol\" 类型。", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "\"use strict\" 指令不能与非简单参数列表一起使用。", "use_strict_directive_used_here_1349": "此处使用了 \"use strict\" 指令。", "with_statements_are_not_allowed_in_an_async_function_block_1300": "不允许在异步函数块中使用 \"with\" 语句。", "with_statements_are_not_allowed_in_strict_mode_1101": "严格模式下不允许使用 \"with\" 语句。", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "\"yield\" 表达式隐式导致 \"any\" 类型，因为它的包含生成器缺少返回类型批注。", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "不能在参数初始化表达式中使用 \"yield\" 表达式。"}